{"name": "@cvte/mobile-framework", "version": "0.1.1-beta.3", "description": "基于Next.js 15构建的移动端底座框架，提供完整的基础设施和组件库", "keywords": ["mobile", "framework", "nextjs", "react", "typescript", "antd-mobile", "zustand", "mobile-foundation"], "author": "CVTE", "license": "MIT", "homepage": "https://github.com/cvte/mobile-framework#readme", "repository": {"type": "git", "url": "git+https://github.com/cvte/mobile-framework.git"}, "bugs": {"url": "https://github.com/cvte/mobile-framework/issues"}, "main": "./dist/index.js", "module": "./dist/index.esm.js", "types": "./dist/index.d.ts", "bin": {"mobile-framework-init": "./scripts/init.js"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.esm.js", "require": "./dist/index.js"}, "./components": {"types": "./dist/components/index.d.ts", "import": "./dist/components/index.esm.js", "require": "./dist/components/index.js"}, "./lib": {"types": "./dist/lib/index.d.ts", "import": "./dist/lib/index.esm.js", "require": "./dist/lib/index.js"}, "./hooks": {"types": "./dist/hooks/index.d.ts", "import": "./dist/hooks/index.esm.js", "require": "./dist/hooks/index.js"}, "./utils": {"types": "./dist/utils/index.d.ts", "import": "./dist/utils/index.esm.js", "require": "./dist/utils/index.js"}, "./styles": "./dist/index.css", "./package.json": "./package.json"}, "files": ["dist", "scripts", "README.md", "LICENSE", "package.json"], "sideEffects": ["*.css", "./src/styles/**/*"], "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}, "publishConfig": {"access": "public", "registry": "https://registry.npmjs.org/"}, "scripts": {"dev": "next dev --turbopack", "build": "next build", "build:lib": "rollup -c rollup.config.js", "build:types": "tsc --project tsconfig.build.json", "build:package": "pnpm run clean:dist && pnpm run build:lib && pnpm run build:types", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "type-check": "tsc --noEmit", "clean": "rm -rf .next out dist", "clean:dist": "rm -rf dist", "analyze": "ANALYZE=true npm run build", "prepublishOnly": "pnpm run build:package", "postinstall": "node scripts/postinstall.js", "pre-publish": "node scripts/pre-publish.js", "test:package": "node scripts/test-package.js", "publish:interactive": "node scripts/publish.js"}, "dependencies": {"antd-mobile": "^5.39.0", "antd-mobile-icons": "^0.3.0", "axios": "^1.9.0", "es-toolkit": "^1.38.0", "next": "15.3.2", "react": "^19.0.0", "react-dom": "^19.0.0", "uuid": "^11.1.0", "zustand": "^5.0.4"}, "devDependencies": {"@types/uuid": "^10.0.0", "@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@rollup/plugin-commonjs": "^28.0.1", "@rollup/plugin-node-resolve": "^16.0.0", "@rollup/plugin-typescript": "^12.1.1", "@rollup/plugin-json": "^6.1.0", "rollup": "^4.28.1", "rollup-plugin-dts": "^6.1.1", "rollup-plugin-peer-deps-external": "^2.2.4", "rollup-plugin-postcss": "^4.0.2", "eslint": "^9", "eslint-config-next": "15.3.2", "prettier": "^3.5.3", "tailwindcss": "^4", "typescript": "^5"}, "peerDependencies": {"react": ">=18.0.0", "react-dom": ">=18.0.0", "next": ">=14.0.0"}, "peerDependenciesMeta": {"next": {"optional": false}}}