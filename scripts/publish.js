#!/usr/bin/env node

/**
 * 自动化发布脚本
 * 执行完整的发布流程
 */

const { execSync } = require('child_process');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

console.log('🚀 移动端底座框架发布脚本\n');

/**
 * 执行命令并输出结果
 */
const runCommand = (command, description) => {
  console.log(`\n📋 ${description}...`);
  try {
    execSync(command, { stdio: 'inherit' });
    console.log(`✅ ${description}完成`);
    return true;
  } catch (error) {
    console.log(`❌ ${description}失败: ${error.message}`);
    return false;
  }
};

/**
 * 询问用户输入
 */
const askQuestion = (question) => {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer);
    });
  });
};

/**
 * 主发布流程
 */
const main = async () => {
  try {
    console.log('🔍 开始发布前检查...\n');

    // 1. 发包前检查
    if (!runCommand('pnpm run pre-publish', '发包前检查')) {
      process.exit(1);
    }

    // 2. 包功能测试
    if (!runCommand('pnpm run test:package', '包功能测试')) {
      process.exit(1);
    }

    // 3. 代码检查
    if (!runCommand('pnpm run lint', '代码规范检查')) {
      process.exit(1);
    }

    // 4. 类型检查
    if (!runCommand('pnpm run type-check', 'TypeScript类型检查')) {
      process.exit(1);
    }

    console.log('\n✅ 所有检查都通过了！\n');

    // 5. 询问版本类型
    console.log('📝 请选择版本更新类型:');
    console.log('1. patch (0.1.0 -> 0.1.1) - bug修复');
    console.log('2. minor (0.1.0 -> 0.2.0) - 新功能');
    console.log('3. major (0.1.0 -> 1.0.0) - 破坏性更改');
    console.log('4. 跳过版本更新');

    const versionChoice = await askQuestion('\n请输入选择 (1-4): ');

    let versionType = '';
    switch (versionChoice.trim()) {
      case '1':
        versionType = 'patch';
        break;
      case '2':
        versionType = 'minor';
        break;
      case '3':
        versionType = 'major';
        break;
      case '4':
        console.log('⏭️  跳过版本更新');
        break;
      default:
        console.log('❌ 无效选择，退出');
        process.exit(1);
    }

    // 6. 更新版本
    if (versionType) {
      if (!runCommand(`npm version ${versionType}`, `版本更新 (${versionType})`)) {
        process.exit(1);
      }
    }

    // 7. 重新构建
    if (!runCommand('pnpm run clean:dist', '清理构建文件')) {
      process.exit(1);
    }

    if (!runCommand('pnpm run build:package', '重新构建包')) {
      process.exit(1);
    }

    // 8. 询问发布类型
    console.log('\n📦 请选择发布类型:');
    console.log('1. 正式发布 (npm publish)');
    console.log('2. 测试发布 (npm publish --tag beta)');
    console.log('3. 仅构建，不发布');

    const publishChoice = await askQuestion('\n请输入选择 (1-3): ');

    switch (publishChoice.trim()) {
      case '1':
        // 正式发布
        const confirmPublish = await askQuestion('\n⚠️  确认要发布到NPM正式版本吗？(y/N): ');
        if (confirmPublish.toLowerCase() === 'y') {
          if (runCommand('npm publish', 'NPM正式发布')) {
            console.log('\n🎉 发布成功！');
            console.log('\n📋 后续步骤:');
            console.log('1. 检查NPM包页面');
            console.log('2. 通知团队成员');
            console.log('3. 更新文档');
            console.log('4. 在实际项目中测试');
          }
        } else {
          console.log('❌ 发布已取消');
        }
        break;

      case '2':
        // 测试发布
        if (runCommand('npm publish --tag beta', 'NPM测试发布')) {
          console.log('\n🧪 测试版本发布成功！');
          console.log('\n安装测试版本: npm install @cvte/mobile-framework@beta');
        }
        break;

      case '3':
        console.log('\n✅ 构建完成，未发布');
        console.log('可以手动运行: npm publish');
        break;

      default:
        console.log('❌ 无效选择');
        break;
    }

  } catch (error) {
    console.log(`\n❌ 发布过程中出现错误: ${error.message}`);
    process.exit(1);
  } finally {
    rl.close();
  }
};

// 运行主流程
main();
