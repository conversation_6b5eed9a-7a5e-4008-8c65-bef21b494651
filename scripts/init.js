#!/usr/bin/env node

/**
 * @cvte/mobile-framework 手动初始化脚本
 * 为业务端提供手动初始化功能
 */

const fs = require('fs');
const path = require('path');

console.log('🚀 移动端底座框架手动初始化...\n');

// 创建目录
const createDirectory = (dirPath) => {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
    console.log(`✅ 创建目录: ${dirPath}`);
  } else {
    console.log(`📁 目录已存在: ${dirPath}`);
  }
};

// 创建文件
const createFile = (filePath, content) => {
  if (!fs.existsSync(filePath)) {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`✅ 创建文件: ${filePath}`);
  } else {
    console.log(`⚠️  文件已存在，跳过: ${filePath}`);
  }
};

// 初始化业务项目结构
const initializeProject = () => {
  const projectRoot = process.cwd();

  // 1. 创建基础目录结构
  const directories = [
    'src/app',
    'src/components',
    'src/lib',
    'src/hooks',
    'src/stores',
    'src/types',
    'src/utils',
    'src/constants',
    'src/styles',
    'public',
  ];

  console.log('📁 创建目录结构...');
  directories.forEach(dir => {
    createDirectory(path.join(projectRoot, dir));
  });

  // 2. 创建基础配置文件
  console.log('\n⚙️  创建配置文件...');

  // Next.js配置
  const nextConfigContent = `import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  reactStrictMode: false,
  experimental: {
    turbo: {
      rules: {
        '*.svg': {
          loaders: ['@svgr/webpack'],
          as: '*.js',
        },
      },
    },
  },
};

export default nextConfig;
`;

  createFile(path.join(projectRoot, 'next.config.ts'), nextConfigContent);

  // TypeScript配置
  const tsconfigContent = `{
  "compilerOptions": {
    "target": "ES2017",
    "lib": ["dom", "dom.iterable", "esnext"],
    "allowJs": true,
    "skipLibCheck": true,
    "strict": true,
    "noEmit": true,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "bundler",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,
    "plugins": [
      {
        "name": "next"
      }
    ],
    "paths": {
      "@/*": ["./src/*"],
      "@/components/*": ["./src/components/*"],
      "@/lib/*": ["./src/lib/*"],
      "@/hooks/*": ["./src/hooks/*"],
      "@/stores/*": ["./src/stores/*"],
      "@/types/*": ["./src/types/*"],
      "@/utils/*": ["./src/utils/*"],
      "@/constants/*": ["./src/constants/*"],
      "@/styles/*": ["./src/styles/*"]
    }
  },
  "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"],
  "exclude": ["node_modules"]
}
`;

  createFile(path.join(projectRoot, 'tsconfig.json'), tsconfigContent);

  // 检查并更新package.json
  console.log('\n📦 检查package.json配置...');
  const packageJsonPath = path.join(projectRoot, 'package.json');

  if (fs.existsSync(packageJsonPath)) {
    try {
      const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
      let needsUpdate = false;

      // 检查必要的scripts
      const requiredScripts = {
        "dev": "next dev --turbopack",
        "build": "next build",
        "start": "next start",
        "lint": "next lint"
      };

      if (!packageJson.scripts) {
        packageJson.scripts = {};
        needsUpdate = true;
      }

      Object.entries(requiredScripts).forEach(([key, value]) => {
        if (!packageJson.scripts[key]) {
          packageJson.scripts[key] = value;
          needsUpdate = true;
          console.log(`✅ 添加脚本: ${key}`);
        } else {
          console.log(`📝 脚本已存在: ${key}`);
        }
      });

      // 检查必要的依赖
      const requiredDependencies = {
        "next": "^15.0.0",
        "react": "^19.0.0",
        "react-dom": "^19.0.0"
      };

      if (!packageJson.dependencies) {
        packageJson.dependencies = {};
        needsUpdate = true;
      }

      Object.entries(requiredDependencies).forEach(([key, value]) => {
        if (!packageJson.dependencies[key]) {
          packageJson.dependencies[key] = value;
          needsUpdate = true;
          console.log(`✅ 添加依赖: ${key}@${value}`);
        } else {
          console.log(`📦 依赖已存在: ${key}`);
        }
      });

      // 检查必要的devDependencies
      const requiredDevDependencies = {
        "@types/node": "^20",
        "@types/react": "^19",
        "@types/react-dom": "^19",
        "typescript": "^5",
        "eslint": "^9",
        "eslint-config-next": "^15.0.0"
      };

      if (!packageJson.devDependencies) {
        packageJson.devDependencies = {};
        needsUpdate = true;
      }

      Object.entries(requiredDevDependencies).forEach(([key, value]) => {
        if (!packageJson.devDependencies[key]) {
          packageJson.devDependencies[key] = value;
          needsUpdate = true;
          console.log(`✅ 添加开发依赖: ${key}@${value}`);
        } else {
          console.log(`🔧 开发依赖已存在: ${key}`);
        }
      });

      if (needsUpdate) {
        fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2), 'utf8');
        console.log('✅ package.json 已更新');
      } else {
        console.log('📝 package.json 无需更新');
      }

    } catch (error) {
      console.log(`⚠️  无法更新package.json: ${error.message}`);
    }
  } else {
    // 创建新的package.json
    const newPackageJson = {
      "name": "mobile-app",
      "version": "0.1.0",
      "private": true,
      "scripts": {
        "dev": "next dev",
        "build": "next build",
        "start": "next start",
        "lint": "next lint"
      },
      "dependencies": {
        "next": "^15.0.0",
        "react": "^19.0.0",
        "react-dom": "^19.0.0",
        "@cvte/mobile-framework": "latest"
      },
      "devDependencies": {
        "@types/node": "^20",
        "@types/react": "^19",
        "@types/react-dom": "^19",
        "typescript": "^5",
        "eslint": "^9",
        "eslint-config-next": "^15.0.0"
      }
    };

    fs.writeFileSync(packageJsonPath, JSON.stringify(newPackageJson, null, 2), 'utf8');
    console.log('✅ 创建新的package.json');
  }

  // 3. 创建应用入口文件
  console.log('\n📱 创建应用文件...');

  const appLayoutContent = `import type { Metadata } from 'next';
import { MobileFrameworkProvider, createMobileApp } from '@cvte/mobile-framework';
import '@cvte/mobile-framework/styles';
import './globals.css';

export const metadata: Metadata = {
  title: '移动端应用',
  description: '基于移动端底座构建的业务应用',
};

// 创建应用实例
const app = createMobileApp({
  appName: '移动端应用',
  version: '1.0.0',
  appDir: './src/app',
  apiBaseUrl: process.env.NEXT_PUBLIC_API_BASE_URL || '/api',
  theme: {
    mode: 'auto',
  },
  logger: {
    level: process.env.NODE_ENV === 'development' ? 'debug' : 'info',
    enableConsole: true,
    enableStorage: process.env.NODE_ENV === 'development',
    enableRemote: process.env.NODE_ENV === 'production',
  },
});

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="zh-CN">
      <body>
        <MobileFrameworkProvider app={app}>
          {children}
        </MobileFrameworkProvider>
      </body>
    </html>
  );
}
`;

  createFile(path.join(projectRoot, 'src/app/layout.tsx'), appLayoutContent);

  // 应用首页
  const appPageContent = `'use client';

import { useMobileFramework } from '@cvte/mobile-framework';
import { Button, Space } from 'antd-mobile';

export default function HomePage() {
  const { config, isInitialized } = useMobileFramework();

  if (!isInitialized) {
    return <div>正在初始化...</div>;
  }

  return (
    <div style={{ padding: '20px' }}>
      <h1>欢迎使用移动端底座</h1>
      <p>应用名称: {config?.appName}</p>
      <p>版本: {config?.version}</p>

      <Space direction="vertical" style={{ width: '100%', marginTop: '20px' }}>
        <Button color="primary" block>
          主要按钮
        </Button>
        <Button block>
          默认按钮
        </Button>
      </Space>
    </div>
  );
}
`;

  createFile(path.join(projectRoot, 'src/app/page.tsx'), appPageContent);

  // 全局样式
  const globalsCssContent = `@import '@cvte/mobile-framework/styles';

/* 业务应用自定义样式 */
body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

/* 自定义主题变量覆盖 */
:root {
  /* 可以在这里覆盖框架的CSS变量 */
  /* --mf-primary: #your-color; */
}
`;

  createFile(path.join(projectRoot, 'src/app/globals.css'), globalsCssContent);

  // 环境变量示例
  const envExampleContent = `# API配置
NEXT_PUBLIC_API_BASE_URL=https://your-api.com/api

# 日志配置
NEXT_PUBLIC_LOG_ENDPOINT=https://your-log-service.com/logs

# 分析配置
NEXT_PUBLIC_GA_ID=GA-XXXXXXX
`;

  createFile(path.join(projectRoot, '.env.example'), envExampleContent);

  console.log('\n🎉 移动端底座项目初始化完成！');
  console.log('\n📖 接下来你需要：');
  console.log('   1. 安装依赖: npm install 或 yarn install 或 pnpm install');
  console.log('   2. 复制 .env.example 为 .env.local 并配置环境变量（可选）');
  console.log('   3. 运行 npm run dev 启动开发服务器');
  console.log('   4. 在 src/app/layout.tsx 中配置应用参数');
  console.log('   5. 开始开发你的业务组件');
  console.log('\n💡 提示：');
  console.log('   - 如果package.json被更新，请重新安装依赖');
  console.log('   - 确保Node.js版本 >= 18');
  console.log('   - 确保已安装@cvte/mobile-framework包');
  console.log('\n📚 更多文档请访问: https://github.com/cvte/mobile-framework');
};

// 主执行逻辑
initializeProject();
