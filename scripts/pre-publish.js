#!/usr/bin/env node

/**
 * 发包前检查脚本
 * 确保所有必要的文件和配置都已准备就绪
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 开始发包前检查...\n');

let hasErrors = false;

/**
 * 检查文件是否存在
 */
const checkFileExists = (filePath, description) => {
  if (fs.existsSync(filePath)) {
    console.log(`✅ ${description}: ${filePath}`);
    return true;
  } else {
    console.log(`❌ ${description}缺失: ${filePath}`);
    hasErrors = true;
    return false;
  }
};

/**
 * 检查目录是否存在且不为空
 */
const checkDirectoryNotEmpty = (dirPath, description) => {
  if (fs.existsSync(dirPath)) {
    const files = fs.readdirSync(dirPath);
    if (files.length > 0) {
      console.log(`✅ ${description}: ${dirPath} (${files.length} 个文件)`);
      return true;
    } else {
      console.log(`❌ ${description}为空: ${dirPath}`);
      hasErrors = true;
      return false;
    }
  } else {
    console.log(`❌ ${description}不存在: ${dirPath}`);
    hasErrors = true;
    return false;
  }
};

/**
 * 检查package.json配置
 */
const checkPackageJson = () => {
  console.log('📦 检查 package.json 配置...');
  
  const packageJsonPath = path.join(process.cwd(), 'package.json');
  if (!checkFileExists(packageJsonPath, 'package.json')) {
    return;
  }

  try {
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    
    // 检查必要字段
    const requiredFields = [
      'name', 'version', 'description', 'main', 'module', 'types',
      'exports', 'files', 'keywords', 'author', 'license'
    ];
    
    requiredFields.forEach(field => {
      if (packageJson[field]) {
        console.log(`✅ ${field}: ${typeof packageJson[field] === 'object' ? 'configured' : packageJson[field]}`);
      } else {
        console.log(`❌ 缺少字段: ${field}`);
        hasErrors = true;
      }
    });

    // 检查scripts
    const requiredScripts = ['build:package', 'prepublishOnly'];
    requiredScripts.forEach(script => {
      if (packageJson.scripts && packageJson.scripts[script]) {
        console.log(`✅ 脚本: ${script}`);
      } else {
        console.log(`❌ 缺少脚本: ${script}`);
        hasErrors = true;
      }
    });

    // 检查是否为私有包
    if (packageJson.private) {
      console.log(`❌ 包被标记为私有，无法发布`);
      hasErrors = true;
    } else {
      console.log(`✅ 包可以公开发布`);
    }

  } catch (error) {
    console.log(`❌ package.json 解析失败: ${error.message}`);
    hasErrors = true;
  }
};

/**
 * 检查构建文件
 */
const checkBuildFiles = () => {
  console.log('\n🏗️  检查构建文件...');
  
  // 检查dist目录
  checkDirectoryNotEmpty('dist', 'dist目录');
  
  // 检查主要构建文件
  const buildFiles = [
    'dist/index.js',
    'dist/index.esm.js',
    'dist/index.d.ts',
    'dist/components/index.js',
    'dist/lib/index.js',
    'dist/hooks/index.js',
    'dist/utils/index.js'
  ];
  
  buildFiles.forEach(file => {
    checkFileExists(file, '构建文件');
  });
};

/**
 * 检查源文件
 */
const checkSourceFiles = () => {
  console.log('\n📁 检查源文件...');
  
  const sourceFiles = [
    'src/index.ts',
    'src/lib/index.ts',
    'src/components/index.ts',
    'src/hooks/index.ts',
    'src/utils/index.ts',
    'src/styles/index.css',
    'src/core/createMobileApp.ts',
    'src/providers/MobileFrameworkProvider.tsx'
  ];
  
  sourceFiles.forEach(file => {
    checkFileExists(file, '源文件');
  });
};

/**
 * 检查配置文件
 */
const checkConfigFiles = () => {
  console.log('\n⚙️  检查配置文件...');
  
  const configFiles = [
    'rollup.config.js',
    'tsconfig.json',
    'tsconfig.build.json',
    'LICENSE',
    'README.md'
  ];
  
  configFiles.forEach(file => {
    checkFileExists(file, '配置文件');
  });
};

/**
 * 检查脚本文件
 */
const checkScripts = () => {
  console.log('\n📜 检查脚本文件...');
  
  const scriptFiles = [
    'scripts/postinstall.js'
  ];
  
  scriptFiles.forEach(file => {
    checkFileExists(file, '脚本文件');
  });
};

/**
 * 主检查流程
 */
const main = () => {
  checkPackageJson();
  checkBuildFiles();
  checkSourceFiles();
  checkConfigFiles();
  checkScripts();
  
  console.log('\n' + '='.repeat(50));
  
  if (hasErrors) {
    console.log('❌ 发包前检查失败！请修复上述问题后重试。');
    process.exit(1);
  } else {
    console.log('✅ 发包前检查通过！可以安全发布。');
    console.log('\n📝 发布步骤：');
    console.log('1. npm version patch|minor|major');
    console.log('2. npm publish');
    process.exit(0);
  }
};

main();
