#!/usr/bin/env node

/**
 * 包功能测试脚本
 * 验证构建后的包是否可以正常导入和使用
 */

const path = require('path');

console.log('🧪 开始测试包功能...\n');

/**
 * 测试模块导入
 */
const testImports = () => {
  console.log('📦 测试模块导入...');

  try {
    // 注意：由于包含React组件和CSS导入，在Node.js环境中可能无法直接require
    // 这是正常的，因为包是为浏览器环境设计的
    console.log('⚠️  注意：此包包含React组件和CSS，在Node.js环境中测试有限制');

    // 测试主入口文件是否存在
    const fs = require('fs');
    const mainModulePath = path.join(process.cwd(), 'dist/index.js');

    if (!fs.existsSync(mainModulePath)) {
      console.log('❌ 主模块文件不存在');
      return;
    }

    console.log('✅ 主模块文件存在');

    // 检查文件内容
    const content = fs.readFileSync(mainModulePath, 'utf8');

    // 检查是否包含关键导出
    const keyExports = ['createMobileApp', 'MobileFrameworkProvider', 'VERSION'];
    const foundExports = keyExports.filter(exp => content.includes(exp));

    console.log(`✅ 找到关键导出: ${foundExports.join(', ')}`);

    // 测试子模块文件
    const subModules = ['lib', 'components', 'hooks', 'utils'];

    subModules.forEach(moduleName => {
      const modulePath = path.join(process.cwd(), `dist/${moduleName}/index.js`);
      if (fs.existsSync(modulePath)) {
        console.log(`✅ ${moduleName} 模块文件存在`);
      } else {
        console.log(`❌ ${moduleName} 模块文件不存在`);
      }
    });

  } catch (error) {
    console.log(`❌ 模块检查失败: ${error.message}`);
  }
};

/**
 * 测试类型定义文件
 */
const testTypeDefinitions = () => {
  console.log('\n📝 测试类型定义文件...');

  const fs = require('fs');

  const typeFiles = [
    'dist/index.d.ts',
    'dist/lib/index.d.ts',
    'dist/components/index.d.ts',
    'dist/hooks/index.d.ts',
    'dist/utils/index.d.ts'
  ];

  typeFiles.forEach(file => {
    if (fs.existsSync(file)) {
      const content = fs.readFileSync(file, 'utf8');
      if (content.trim().length > 0) {
        console.log(`✅ ${file} 存在且非空`);
      } else {
        console.log(`⚠️  ${file} 存在但为空`);
      }
    } else {
      console.log(`❌ ${file} 不存在`);
    }
  });
};

/**
 * 测试样式文件
 */
const testStyleFiles = () => {
  console.log('\n🎨 测试样式文件...');

  const fs = require('fs');

  const styleFiles = [
    'dist/index.css',
    'src/styles/index.css'
  ];

  styleFiles.forEach(file => {
    if (fs.existsSync(file)) {
      const content = fs.readFileSync(file, 'utf8');
      if (content.includes('mobile-framework')) {
        console.log(`✅ ${file} 包含框架样式`);
      } else {
        console.log(`⚠️  ${file} 可能缺少框架样式`);
      }
    } else {
      console.log(`❌ ${file} 不存在`);
    }
  });
};

/**
 * 测试包大小
 */
const testPackageSize = () => {
  console.log('\n📊 测试包大小...');

  const fs = require('fs');

  const getFileSize = (filePath) => {
    try {
      const stats = fs.statSync(filePath);
      return (stats.size / 1024).toFixed(2) + ' KB';
    } catch {
      return 'N/A';
    }
  };

  const files = [
    'dist/index.js',
    'dist/index.esm.js',
    'dist/index.css'
  ];

  files.forEach(file => {
    const size = getFileSize(file);
    console.log(`📦 ${file}: ${size}`);
  });
};

/**
 * 测试package.json配置
 */
const testPackageConfig = () => {
  console.log('\n⚙️  测试package.json配置...');

  const fs = require('fs');
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));

  // 检查入口文件是否存在
  const entryPoints = [
    packageJson.main,
    packageJson.module,
    packageJson.types
  ];

  entryPoints.forEach((entry, index) => {
    const names = ['main', 'module', 'types'];
    if (entry && fs.existsSync(entry)) {
      console.log(`✅ ${names[index]} 入口文件存在: ${entry}`);
    } else {
      console.log(`❌ ${names[index]} 入口文件不存在: ${entry}`);
    }
  });

  // 检查exports配置
  if (packageJson.exports) {
    console.log('✅ exports 配置存在');
    Object.keys(packageJson.exports).forEach(key => {
      console.log(`   - ${key}: configured`);
    });
  } else {
    console.log('❌ exports 配置缺失');
  }
};

/**
 * 模拟业务端使用场景
 */
const testBusinessUsage = () => {
  console.log('\n🏢 模拟业务端使用场景...');

  // 由于包含React组件和CSS，在Node.js环境中无法直接测试
  // 但我们可以检查构建文件的结构和内容

  const fs = require('fs');
  const mainModulePath = path.join(process.cwd(), 'dist/index.js');

  if (!fs.existsSync(mainModulePath)) {
    console.log('❌ 主模块文件不存在');
    return;
  }

  const content = fs.readFileSync(mainModulePath, 'utf8');

  // 检查关键功能是否存在
  const checks = [
    { name: 'createMobileApp', pattern: /createMobileApp.*=.*function|function.*createMobileApp|const.*createMobileApp/ },
    { name: 'MobileFrameworkProvider', pattern: /MobileFrameworkProvider/ },
    { name: 'VERSION', pattern: /VERSION.*=.*['"`]/ },
    { name: 'apiClient', pattern: /apiClient/ },
    { name: 'logger', pattern: /logger/ },
    { name: 'cacheManager', pattern: /cacheManager/ },
  ];

  console.log('📋 检查关键功能:');
  checks.forEach(check => {
    if (check.pattern.test(content)) {
      console.log(`✅ ${check.name} 功能存在`);
    } else {
      console.log(`❌ ${check.name} 功能缺失`);
    }
  });

  // 检查导出语法
  if (content.includes('exports.')) {
    console.log('✅ 使用CommonJS导出格式');
  } else if (content.includes('export ')) {
    console.log('⚠️  使用ES模块导出格式');
  }

  console.log('\n💡 实际使用测试需要在React/Next.js环境中进行');
};

/**
 * 主测试流程
 */
const main = () => {
  testImports();
  testTypeDefinitions();
  testStyleFiles();
  testPackageSize();
  testPackageConfig();
  testBusinessUsage();

  console.log('\n' + '='.repeat(50));
  console.log('🎉 包功能测试完成！');
  console.log('\n💡 提示：');
  console.log('   - 如果所有测试都通过，包已准备好发布');
  console.log('   - 建议在实际项目中进一步测试');
  console.log('   - 可以使用 npm pack 生成本地包进行测试');
};

main();
