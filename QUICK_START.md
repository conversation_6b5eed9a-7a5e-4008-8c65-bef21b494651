# 🚀 移动端底座框架快速开始

## 📦 安装

```bash
# 安装框架
npm install @cvte/mobile-framework@beta

# 或使用其他包管理器
yarn add @cvte/mobile-framework@beta
pnpm add @cvte/mobile-framework@beta
```

## 🛠️ 初始化项目

### 方法一：使用初始化命令（推荐）

```bash
# 安装后运行初始化命令
npx mobile-framework-init

# 或者如果全局安装了
mobile-framework-init
```

**注意**：初始化命令会：
- 创建项目目录结构
- 生成配置文件（next.config.ts, tsconfig.json）
- 创建应用入口文件
- **自动添加必要的scripts到package.json**
- **添加必要的依赖到package.json**

如果package.json被更新，请重新安装依赖：
```bash
npm install
# 或
yarn install
# 或
pnpm install
```

### 方法二：手动创建文件

如果初始化命令不工作，可以手动创建以下文件：

#### 1. 创建目录结构

```
your-project/
├── src/
│   ├── app/
│   ├── components/
│   ├── lib/
│   ├── hooks/
│   ├── stores/
│   ├── types/
│   ├── utils/
│   ├── constants/
│   └── styles/
└── public/
```

#### 2. 创建 `src/app/layout.tsx`

```typescript
import type { Metadata } from 'next';
import { MobileFrameworkProvider, createMobileApp } from '@cvte/mobile-framework';
import '@cvte/mobile-framework/styles';
import './globals.css';

export const metadata: Metadata = {
  title: '移动端应用',
  description: '基于移动端底座构建的业务应用',
};

// 创建应用实例
const app = createMobileApp({
  appName: '移动端应用',
  version: '1.0.0',
  appDir: './src/app',
  apiBaseUrl: process.env.NEXT_PUBLIC_API_BASE_URL || '/api',
  theme: {
    mode: 'auto',
  },
  logger: {
    level: process.env.NODE_ENV === 'development' ? 'debug' : 'info',
    enableConsole: true,
    enableStorage: process.env.NODE_ENV === 'development',
    enableRemote: process.env.NODE_ENV === 'production',
  },
});

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="zh-CN">
      <body>
        <MobileFrameworkProvider app={app}>
          {children}
        </MobileFrameworkProvider>
      </body>
    </html>
  );
}
```

#### 3. 创建 `src/app/page.tsx`

```typescript
'use client';

import { useMobileFramework } from '@cvte/mobile-framework';
import { Button, Space } from 'antd-mobile';

export default function HomePage() {
  const { config, isInitialized } = useMobileFramework();

  if (!isInitialized) {
    return <div>正在初始化...</div>;
  }

  return (
    <div style={{ padding: '20px' }}>
      <h1>欢迎使用移动端底座</h1>
      <p>应用名称: {config?.appName}</p>
      <p>版本: {config?.version}</p>

      <Space direction="vertical" style={{ width: '100%', marginTop: '20px' }}>
        <Button color="primary" block>
          主要按钮
        </Button>
        <Button block>
          默认按钮
        </Button>
      </Space>
    </div>
  );
}
```

#### 4. 创建 `src/app/globals.css`

```css
@import '@cvte/mobile-framework/styles';

/* 业务应用自定义样式 */
body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

/* 自定义主题变量覆盖 */
:root {
  /* 可以在这里覆盖框架的CSS变量 */
  /* --mf-primary: #your-color; */
}
```

#### 5. 创建 `next.config.ts`

```typescript
import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  reactStrictMode: false,
  experimental: {
    turbo: {
      rules: {
        '*.svg': {
          loaders: ['@svgr/webpack'],
          as: '*.js',
        },
      },
    },
  },
};

export default nextConfig;
```

#### 6. 创建 `tsconfig.json`

```json
{
  "compilerOptions": {
    "target": "ES2017",
    "lib": ["dom", "dom.iterable", "esnext"],
    "allowJs": true,
    "skipLibCheck": true,
    "strict": true,
    "noEmit": true,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "bundler",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,
    "plugins": [
      {
        "name": "next"
      }
    ],
    "paths": {
      "@/*": ["./src/*"],
      "@/components/*": ["./src/components/*"],
      "@/lib/*": ["./src/lib/*"],
      "@/hooks/*": ["./src/hooks/*"],
      "@/stores/*": ["./src/stores/*"],
      "@/types/*": ["./src/types/*"],
      "@/utils/*": ["./src/utils/*"],
      "@/constants/*": ["./src/constants/*"],
      "@/styles/*": ["./src/styles/*"]
    }
  },
  "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"],
  "exclude": ["node_modules"]
}
```

## 📦 package.json配置

初始化命令会自动为您的package.json添加必要的配置：

### 必要的scripts
```json
{
  "scripts": {
    "dev": "next dev",
    "build": "next build",
    "start": "next start",
    "lint": "next lint"
  }
}
```

### 必要的依赖
```json
{
  "dependencies": {
    "next": "^15.0.0",
    "react": "^19.0.0",
    "react-dom": "^19.0.0",
    "@cvte/mobile-framework": "latest"
  },
  "devDependencies": {
    "@types/node": "^20",
    "@types/react": "^19",
    "@types/react-dom": "^19",
    "typescript": "^5",
    "eslint": "^9",
    "eslint-config-next": "^15.0.0"
  }
}
```

**如果您的package.json缺少这些配置**，可以：
1. 重新运行 `npx mobile-framework-init`
2. 或手动添加上述配置

## 🚀 启动开发

```bash
# 首先安装依赖（如果package.json被更新）
npm install

# 然后启动开发服务器
npm run dev

# 或使用其他包管理器
yarn install && yarn dev
pnpm install && pnpm dev
```

## 📱 开始开发

### 使用框架功能

```typescript
// 使用API客户端
import { apiClient } from '@cvte/mobile-framework/lib';

// 使用Hooks
import { useLocalStorage, useNetworkStatus } from '@cvte/mobile-framework/hooks';

// 使用工具函数
import { formatDateTime, getDeviceInfo } from '@cvte/mobile-framework/utils';
```

### 环境变量配置

创建 `.env.local` 文件：

```bash
# API配置
NEXT_PUBLIC_API_BASE_URL=https://your-api.com/api

# 日志配置
NEXT_PUBLIC_LOG_ENDPOINT=https://your-log-service.com/logs

# 分析配置
NEXT_PUBLIC_GA_ID=GA-XXXXXXX
```

## 🎯 核心特性

- ✅ **插件化配置**：获取业务端的Next.js appDir等配置
- ✅ **自动初始化**：框架启动时自动配置各个模块
- ✅ **模块化导入**：按需导入，减少包大小
- ✅ **TypeScript支持**：完整的类型定义
- ✅ **移动端优化**：响应式设计和触摸友好

## 🆘 故障排除

### 常见问题

1. **初始化命令不工作**
   - 确保已安装包：`npm list @cvte/mobile-framework`
   - 尝试使用 `npx mobile-framework-init`
   - 或手动创建文件

2. **样式不生效**
   - 确保导入了样式：`import '@cvte/mobile-framework/styles';`
   - 检查CSS导入顺序

3. **TypeScript错误**
   - 确保tsconfig.json配置正确
   - 检查路径别名配置

### 获取帮助

- 📖 查看完整文档：README.md
- 🐛 报告问题：GitHub Issues
- 💬 讨论交流：GitHub Discussions

---

**🎉 开始构建您的移动端应用吧！**
