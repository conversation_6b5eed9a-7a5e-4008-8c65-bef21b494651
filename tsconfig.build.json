{"extends": "./tsconfig.json", "compilerOptions": {"target": "ES2017", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": false, "skipLibCheck": true, "strict": true, "noEmit": false, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "react-jsx", "declaration": true, "declarationMap": true, "outDir": "./dist", "rootDir": "./src", "removeComments": false, "sourceMap": true}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", ".next", "src/app/**/*", "**/*.test.ts", "**/*.test.tsx", "**/*.spec.ts", "**/*.spec.tsx"]}