# 移动端底座用户视觉架构

## 1. 用户界面层次结构

```mermaid
%%{init: {'theme': 'base', 'themeVariables': { 'primaryColor': '#b5add3', 'primaryTextColor': '#2c3e50', 'primaryBorderColor': '#9e95bb', 'lineColor': '#9e95bb', 'secondaryColor': '#d6c0b0', 'tertiaryColor': '#f9f9f9' }}}%%
graph TD
    User[用户] --> |访问| App[移动应用]
    
    subgraph 应用视图层
        App --> Shell[应用外壳]
        Shell --> Navigation[导航系统]
        Shell --> Pages[页面容器]
        Shell --> Modals[模态对话框]
        Shell --> Notifications[通知提示]
        
        Navigation --> NavBar[导航栏]
        Navigation --> Tabs[底部标签栏]
        Navigation --> DrawerMenu[侧边抽屉菜单]
        
        Pages --> Auth[认证页面]
        Pages --> Dashboard[仪表盘页面]
        Pages --> Todo[代办页面]
        Pages --> Lists[列表页面]
        Pages --> Details[详情页面]
        Pages --> Charts[图表页面]
        
        Modals --> FormModals[表单弹窗]
        Modals --> ConfirmModals[确认弹窗]
        Modals --> InfoModals[信息弹窗]
    end
    
    subgraph 组件层
        Pages -.-> UIComponents[UI组件]
        Modals -.-> UIComponents
        
        UIComponents --> FormTemplates[表单模板]
        UIComponents --> ListTemplates[列表模板]
        UIComponents --> ChartTemplates[图表模板]
        
        FormTemplates --> FormItems[表单项组件]
        ListTemplates --> ListItems[列表项组件]
        
        FormItems --> BaseComponents[基础组件]
        ListItems --> BaseComponents
        ChartTemplates --> BaseComponents
    end
    
    classDef user fill:#e2a4a4,stroke:#d98c8c,stroke-width:2px,color:#4d3939;
    classDef app fill:#a8d1ce,stroke:#89b0ad,stroke-width:2px,color:#2d3e3d;
    classDef viewLayer fill:#e8f0f7,stroke:#a8c0d6,stroke-width:2px,color:#3a4a5c;
    classDef navComponents fill:#d6c0b0,stroke:#b7a58f,stroke-width:2px,color:#3e342c;
    classDef pageComponents fill:#b0c4d6,stroke:#8fa5b7,stroke-width:2px,color:#2c3e50;
    classDef modalComponents fill:#f7e8aa,stroke:#d9cb94,stroke-width:2px,color:#4d452d;
    classDef uiComponents fill:#b5add3,stroke:#9e95bb,stroke-width:2px,color:#2c3e50;
    classDef baseComponents fill:#a8d1ce,stroke:#89b0ad,stroke-width:2px,color:#2d3e3d;
    
    class User user;
    class App app;
    class Shell,Navigation,Pages,Modals,Notifications viewLayer;
    class NavBar,Tabs,DrawerMenu navComponents;
    class Auth,Dashboard,Todo,Lists,Details,Charts pageComponents;
    class FormModals,ConfirmModals,InfoModals modalComponents;
    class UIComponents,FormTemplates,ListTemplates,ChartTemplates uiComponents;
    class FormItems,ListItems,BaseComponents baseComponents;
```

## 2. 页面导航流程

```mermaid
%%{init: {'theme': 'base', 'themeVariables': { 'primaryColor': '#a8c0d6', 'primaryTextColor': '#2c3e50', 'primaryBorderColor': '#8fa5b7', 'lineColor': '#8fa5b7', 'secondaryColor': '#d6c0b0', 'tertiaryColor': '#f9f9f9' }}}%%
graph LR
    Login[登录页面] --> |认证成功| Home[首页/仪表盘]
    
    Home --> |导航| Todo[代办模块]
    Home --> |导航| Module1[模块1]
    Home --> |导航| Module2[模块2]
    Home --> |导航| Settings[设置页面]
    
    Todo --> |查看详情| TodoDetail[代办详情]
    TodoDetail --> |返回| Todo
    
    Module1 --> |查看列表| List1[列表页面]
    Module1 --> |查看详情| Detail1[详情页面]
    
    List1 --> |选择项| Detail1
    Detail1 --> |返回| List1
    
    Module2 --> |查看列表| List2[列表页面]
    Module2 --> |查看详情| Detail2[详情页面]
    Module2 --> |查看图表| Chart[图表页面]
    
    List2 --> |选择项| Detail2
    Detail2 --> |返回| List2
    Chart --> |返回| Module2
    
    classDef entry fill:#e2a4a4,stroke:#d98c8c,stroke-width:2px,color:#4d3939;
    classDef main fill:#a8d1ce,stroke:#89b0ad,stroke-width:2px,color:#2d3e3d;
    classDef module fill:#b5add3,stroke:#9e95bb,stroke-width:2px,color:#2c3e50;
    classDef page fill:#d6c0b0,stroke:#b7a58f,stroke-width:2px,color:#3e342c;
    
    class Login entry;
    class Home main;
    class Todo,Module1,Module2,Settings module;
    class TodoDetail,List1,Detail1,List2,Detail2,Chart page;
```

## 3. 交互流程图

```mermaid
%%{init: {'theme': 'base', 'themeVariables': { 'primaryColor': '#b5add3', 'primaryTextColor': '#FFF', 'primaryBorderColor': '#9e95bb', 'lineColor': '#a8d1ce', 'secondaryColor': '#a8d1ce', 'tertiaryColor': '#f9f9f9' }}}%%
sequenceDiagram
    participant User as 用户
    participant UI as 界面
    participant State as 状态管理
    participant API as API服务
    
    User->>UI: 进入应用
    UI->>State: 初始化状态
    State->>API: 请求数据
    API-->>State: 返回数据
    State-->>UI: 更新视图
    UI-->>User: 展示内容
    
    User->>UI: 交互操作
    UI->>State: 触发状态变更
    
    alt 本地操作
        State-->>UI: 直接更新视图
        UI-->>User: 展示反馈
    else 需要网络请求
        State->>API: 发送请求
        UI-->>User: 显示加载状态
        API-->>State: 返回结果
        State-->>UI: 更新视图
        UI-->>User: 展示结果和反馈
    end
    
    Note over UI,State: 使用zustand管理状态
    Note over State,API: 使用AxiosHook/AxiosTools处理API请求
```

## 4. 主题与视觉系统

```mermaid
%%{init: {'theme': 'base', 'themeVariables': { 'primaryColor': '#a8c0d6', 'primaryTextColor': '#2c3e50', 'primaryBorderColor': '#8fa5b7', 'lineColor': '#8fa5b7', 'secondaryColor': '#d6c0b0', 'tertiaryColor': '#f9f9f9' }}}%%
graph TD
    Theme[主题系统] --> Colors[色彩系统]
    Theme --> Spacing[间距系统]
    Theme --> Components[组件主题]
    
    Colors --> Primary[主色调]
    Colors --> Secondary[辅助色]
    Colors --> Neutral[中性色]
    Colors --> Functional[功能色]
    
    Primary --> Brand[品牌色]
    Secondary --> Accent[强调色]
    Neutral --> Background[背景色]
    Neutral --> Text[文本色]
    Functional --> Success[成功色]
    Functional --> Warning[警告色]
    Functional --> Error[错误色]
    Functional --> Info[信息色]
    
    Components --> FormTheme[表单主题]
    Components --> ListTheme[列表主题]
    Components --> ChartTheme[图表主题]
    Components --> NavTheme[导航主题]
    
    subgraph 响应式适配
        MediaQueries[媒体查询] --> MobileLayout[移动布局]
        MediaQueries --> TabletLayout[平板布局]
    
    end
    
    classDef themeSystem fill:#b5add3,stroke:#9e95bb,stroke-width:2px,color:#2c3e50;
    classDef colorSystem fill:#a8d1ce,stroke:#89b0ad,stroke-width:2px,color:#2d3e3d;
    classDef colorCategory fill:#d6c0b0,stroke:#b7a58f,stroke-width:2px,color:#3e342c;
    classDef specificColor fill:#f7e8aa,stroke:#d9cb94,stroke-width:2px,color:#4d452d;
    classDef componentTheme fill:#e8f0f7,stroke:#a8c0d6,stroke-width:2px,color:#3a4a5c;
    classDef responsive fill:#f9f9f9,stroke:#d9d9d9,stroke-width:2px,color:#2c3e50;
    
    class Theme themeSystem;
    class Colors,Spacing,Components themeSystem;
    class Primary,Secondary,Neutral,Functional colorSystem;
    class Brand,Accent,Background,Text,Success,Warning,Error,Info specificColor;
    class FormTheme,ListTheme,ChartTheme,NavTheme componentTheme;
    class MediaQueries,MobileLayout,TabletLayout,DesktopLayout responsive;
```

## 5. 用户反馈系统

```mermaid
%%{init: {'theme': 'base', 'themeVariables': { 'primaryColor': '#b5add3', 'primaryTextColor': '#2c3e50', 'primaryBorderColor': '#9e95bb', 'lineColor': '#9e95bb', 'secondaryColor': '#d6c0b0', 'tertiaryColor': '#f9f9f9' }}}%%
graph TD
    Feedback[用户反馈系统] --> Loading[加载状态]
    Feedback --> Notification[通知提示]
    Feedback --> ValidationFeedback[表单验证反馈]
    Feedback --> ErrorHandling[错误处理]
    
    Loading --> Skeleton[骨架屏]
    Loading --> Spinner[加载动画]
    Loading --> ProgressBar[进度条]
    
    Notification --> Toast[轻提示]
    Notification --> Alert[警告框]
    Notification --> Banner[横幅通知]
    Notification --> Badge[徽标]
    
    ValidationFeedback --> InlineError[内联错误]
    ValidationFeedback --> FieldHighlight[字段高亮]
    ValidationFeedback --> FormMessage[表单消息]
    
    ErrorHandling --> NetworkError[网络错误]
    ErrorHandling --> ValidationError[验证错误]
    ErrorHandling --> SystemError[系统错误]
    ErrorHandling --> RecoveryAction[恢复操作]
    
    classDef feedbackSystem fill:#b5add3,stroke:#9e95bb,stroke-width:2px,color:#2c3e50;
    classDef feedbackCategory fill:#a8d1ce,stroke:#89b0ad,stroke-width:2px,color:#2d3e3d;
    classDef feedbackComponent fill:#d6c0b0,stroke:#b7a58f,stroke-width:2px,color:#3e342c;
    
    class Feedback feedbackSystem;
    class Loading,Notification,ValidationFeedback,ErrorHandling feedbackCategory;
    class Skeleton,Spinner,ProgressBar,Toast,Alert,Banner,Badge,InlineError,FieldHighlight,FormMessage,NetworkError,ValidationError,SystemError,RecoveryAction feedbackComponent;
```

## 总结

以上视觉架构图从用户体验和视觉设计的角度描述了移动端底座应用的整体结构。这些图表展示了用户如何与应用交互、导航流程、主题系统以及反馈机制，为前端开发提供了清晰的视觉指导。该架构基于Next.js、Ant Design Mobile、Zustand等技术栈构建，遵循了现代移动应用的设计原则和最佳实践。
