# 移动端底座系统架构

## 1. 系统全景架构图

```mermaid
%%{init: {'theme': 'base', 'themeVariables': { 'primaryColor': '#a8c0d6', 'primaryTextColor': '#2c3e50', 'primaryBorderColor': '#6d8cb0', 'lineColor': '#6d8cb0', 'secondaryColor': '#8fb0a9', 'tertiaryColor': '#f9f9f9' }}}%%
graph TB
    Client[客户端] -->|"HTTP/HTTPS"| LoadBalancer[负载均衡器]
    LoadBalancer -->|请求分发| NextJS[Next.js服务]
    
    subgraph 前端系统
        NextJS -->|渲染| SSR[服务端渲染]
        NextJS -->|静态生成| SSG[静态页面]
        NextJS -->|API路由| ServerAPI[API路由]
        NextJS -->|认证| Auth[认证服务]
        ServerAPI -->|业务| Services[业务服务]

        Auth -->|验证| IdentityProvider[身份提供者]
        
        subgraph 客户端渲染
            SSR --> Hydration[组件水合]
            SSG --> CSR[客户端交互]
            Hydration --> ClientComponents[客户端组件]
            CSR --> ClientComponents
        end
    end
    
    subgraph 后端系统
        Services -->|请求| BFF[后端API]
        BFF -->|数据| Database[数据服务]
        
        Services -->|读写| Database
    end
    
    subgraph 基础设施
        NextJS -->|监控| Monitoring[监控系统]
        NextJS -->|日志| Logging[日志系统]
        BFF -->|监控| Monitoring
        BFF -->|日志| Logging
        
        LoadBalancer -->|扩缩容| Autoscaling[自动伸缩]
        Autoscaling -->|管理| NextJS
    end
    
    classDef client fill:#e2a4a4,stroke:#d98c8c,stroke-width:2px,color:#4d3939;
    classDef infrastructure fill:#f7e8aa,stroke:#d9cb94,stroke-width:2px,color:#4d452d;
    classDef frontend fill:#a8d1ce,stroke:#89b0ad,stroke-width:2px,color:#2d3e3d;
    classDef backend fill:#b5add3,stroke:#9e95bb,stroke-width:2px,color:#2c3e50;
    classDef data fill:#b0c4d6,stroke:#8fa5b7,stroke-width:2px,color:#2c3e50;
    classDef clientside fill:#e8f0f7,stroke:#a8c0d6,stroke-width:2px,color:#3a4a5c;
    
    class Client client;
    class LoadBalancer,Monitoring,Logging,Backup,Autoscaling infrastructure;
    class NextJS,SSR,SSG,ServerAPI frontend;
    class BFF,Auth,Services,ExternalAPI backend;
    class Database data;
    class Hydration,CSR,ClientComponents clientside;
```

## 2. 系统逻辑分层架构

```mermaid
%%{init: {'theme': 'base', 'themeVariables': { 'primaryColor': '#a8c0d6', 'primaryTextColor': '#2c3e50', 'primaryBorderColor': '#6d8cb0', 'lineColor': '#6d8cb0', 'secondaryColor': '#8fb0a9', 'tertiaryColor': '#f9f9f9' }}}%%
graph TB
    User[用户] -->|交互| Presentation[表现层]
    
    subgraph 系统分层
        Presentation -->|依赖| BusinessLogic[业务逻辑层]
        BusinessLogic -->|依赖| DataAccess[数据访问层]
        DataAccess -->|依赖| Infrastructure[基础设施层]
    end
    
    subgraph 表现层
        UI[用户界面] --> WebUI[Web界面]
        UI --> Components[UI组件库]
        
        WebUI --> Responsive[响应式设计]
        Components --> AntdMobile[Antd Mobile]
    end
    
    subgraph 业务逻辑层
        ApplicationServices[应用服务] --> StateManagement[状态管理]
        ApplicationServices --> BizWorkflows[业务工作流]
    end
    
    subgraph 数据访问层
        Repository[仓储] --> APIClient[API客户端]
        Repository --> LocalStorage[本地存储]
        Repository --> Cache[缓存管理]
        
        APIClient --> REST[后端API]
    end
    
    subgraph 基础设施层
        Network[网络处理] --> Axios[Axios]
        
        Persistence[持久化] --> IndexedDB[IndexedDB]
        Persistence --> LocalForage[LocalForage]
        
        CrossCutting[横切关注点] --> Logging[日志]
        CrossCutting --> Monitoring[监控]
    end
    
    classDef user fill:#e2a4a4,stroke:#d98c8c,stroke-width:2px,color:#4d3939;
    classDef layer fill:#b0c4d6,stroke:#8fa5b7,stroke-width:2px,color:#2c3e50;
    classDef presentation fill:#a8d1ce,stroke:#89b0ad,stroke-width:2px,color:#2d3e3d;
    classDef business fill:#b5add3,stroke:#9e95bb,stroke-width:2px,color:#2c3e50;
    classDef data fill:#d6c0b0,stroke:#b7a58f,stroke-width:2px,color:#3e342c;
    classDef infra fill:#f7e8aa,stroke:#d9cb94,stroke-width:2px,color:#4d452d;
    classDef detail fill:#e8f0f7,stroke:#a8c0d6,stroke-width:2px,color:#3a4a5c;
    
    class User user;
    class Presentation,BusinessLogic,DataAccess,Infrastructure layer;
    class UI,MobileUI,WebUI,Components,ReactNative,Responsive,AntdMobile presentation;
    class Domain,ApplicationServices,Entities,UseCases,Services,StateManagement,BizWorkflows,Validation business;
    class Repository,APIClient,LocalStorage,Cache,REST,GraphQL data;
    class Network,Persistence,CrossCutting,HTTP,WebSocket,IndexedDB,LocalForage,Logging,Security,Monitoring infra;
```

## 3. 核心模块架构图

```mermaid
%%{init: {'theme': 'base', 'themeVariables': { 'primaryColor': '#b5add3', 'primaryTextColor': '#2c3e50', 'primaryBorderColor': '#9e95bb', 'lineColor': '#9e95bb', 'secondaryColor': '#d6c0b0', 'tertiaryColor': '#f9f9f9' }}}%%
graph TB
    Core[核心模块] --> Auth[认证授权模块]
    Core --> UI[UI组件模块]
    Core --> State[状态管理模块]
    Core --> Routing[路由导航模块]
    Core --> I18N[国际化模块]
    Core --> Utility[工具模块]
    Core --> PluginMgmt[插件管理模块]
    
    subgraph 认证授权模块
        Auth --> Login[登录认证]
        Auth --> Permission[权限管理]
        Auth --> Session[会话管理]
        
        Login --> WeChat[企业微信集成]
        Login --> OAuth[OAuth认证]
        
        Permission --> RBAC[角色权限控制]
        Permission --> ACL[访问控制列表]
    end
    
    subgraph UI组件模块
        UI --> Base[基础组件]
        UI --> Form[表单组件]
        UI --> List[列表组件]
        UI --> Chart[图表组件]
        UI --> Notification[通知组件]
        
        Base --> Layout[布局组件]
        Base --> Navigation[导航组件]
        
        Form --> FormTemplates[表单模板]
        List --> ListTemplates[列表模板]
    end
    
    subgraph 状态管理模块
        State --> GlobalState[全局状态]
        State --> LocalState[局部状态]
        State --> Persistence[持久化]
        
        GlobalState --> AuthStore[认证状态]
        GlobalState --> UIStore[UI状态]
        GlobalState --> FeatureStore[功能状态]
        
        LocalState --> ComponentState[组件状态]
        LocalState --> PageState[页面状态]
    end
    
    subgraph 路由导航模块
        Routing --> Routes[路由定义]
        Routing --> Guards[路由守卫]
        Routing --> Params[路由参数]
        
        Routes --> Static[静态路由]
        Routes --> Dynamic[动态路由]
        
        Guards --> AuthGuard[认证守卫]
        Guards --> RoleGuard[角色守卫]
    end

    subgraph 插件管理模块
        PluginMgmt --> PluginRegistry[插件注册表]
        PluginMgmt --> PluginLoader[插件加载器]
        PluginMgmt --> PluginLifecycle[插件生命周期管理]
        PluginMgmt --> HookSystem[钩子系统]
    end

    PluginMgmt -.->|影响| UI
    PluginMgmt -.->|影响| Routing
    PluginMgmt -.->|影响| State
    PluginMgmt -.->|影响| Auth
    
    classDef coreModule fill:#b5add3,stroke:#9e95bb,stroke-width:2px,color:#2c3e50;
    classDef moduleGroup fill:#e8f0f7,stroke:#a8c0d6,stroke-width:2px,color:#3a4a5c;
    classDef submodule fill:#a8d1ce,stroke:#89b0ad,stroke-width:2px,color:#2d3e3d;
    classDef feature fill:#d6c0b0,stroke:#b7a58f,stroke-width:2px,color:#3e342c;
    classDef pluginModule fill:#f9f9f9,stroke:#d9d9d9,stroke-width:2px,color:#2c3e50;
    
    class Core coreModule;
    class Auth,UI,State,Routing,I18N,Utility,PluginMgmt coreModule;
    class AuthModule,UIModule,StateModule,RoutingModule moduleGroup;
    class PluginManagementModule pluginModule;
    class Login,Permission,Session,Base,Form,List,Chart,Feedback,GlobalState,LocalState,Persistence,Routes,Guards,Params submodule;
    class WeChat,OAuth,RBAC,ACL,Layout,Navigation,FormTemplates,ListTemplates,AuthStore,UIStore,FeatureStore,ComponentState,PageState,Static,Dynamic,AuthGuard,RoleGuard feature;
    class PluginRegistry,PluginLoader,PluginLifecycle,HookSystem feature;

    %% 设置显示名称
    AuthModule["认证授权模块"]
    UIModule["UI组件模块"]
    StateModule["状态管理模块"]
    RoutingModule["路由导航模块"]
    PluginManagementModule["插件管理模块"]
```

## 4. 部署架构图

```mermaid
%%{init: {'theme': 'base', 'themeVariables': { 'primaryColor': '#a8c0d6', 'primaryTextColor': '#2c3e50', 'primaryBorderColor': '#6d8cb0', 'lineColor': '#6d8cb0', 'secondaryColor': '#8fb0a9', 'tertiaryColor': '#f9f9f9' }}}%%
graph TB
    Developer[开发人员] -->|提交代码| GitRepo[Git仓库]
    
    subgraph CI/CD流水线
        GitRepo -->|触发| Build[构建作业]
        Build -->|执行| Test[测试作业]
        Test -->|执行| Deploy[部署作业]
    end
    
    subgraph 环境
        Deploy -->|部署到| Dev[开发环境]
        Deploy -->|部署到| Sit[测试环境]
        Deploy -->|部署到| Uat[预发布环境]
        Deploy -->|部署到| Production[生产环境]
    end
    
    subgraph 生产基础设施
        Sit -->|部署| CDN[CDN]
        Uat -->|部署| CDN[CDN]
        Production -->|部署| CDN[CDN]

        
        Internet[互联网] -->|访问| CDN
        CDN -->|静态资源| Client[客户端]
        Client -->|动态请求| LoadBalancer[负载均衡器]
        LoadBalancer -->|分发| AppCluster
        AppCluster -->|读写| DBServers
        
        AppCluster -->|上报| Monitoring[监控系统]
        AppCluster -->|记录| Logging[日志系统]
        Monitoring -->|告警| OnCall[值班人员]
    end
    
    classDef human fill:#e2a4a4,stroke:#d98c8c,stroke-width:2px,color:#4d3939;
    classDef cicd fill:#b5add3,stroke:#9e95bb,stroke-width:2px,color:#2c3e50;
    classDef env fill:#a8d1ce,stroke:#89b0ad,stroke-width:2px,color:#2d3e3d;
    classDef infra fill:#d6c0b0,stroke:#b7a58f,stroke-width:2px,color:#3e342c;
    classDef external fill:#f7e8aa,stroke:#d9cb94,stroke-width:2px,color:#4d452d;
    
    class Developer,OnCall human;
    class GitRepo,Build,Test,Deploy cicd;
    class Dev,Sit,Uat,Production env;
    class CDN,AppCluster,DBServers,LoadBalancer,Monitoring,Logging infra;
    class Internet,Client external;
```

## 5. 技术栈架构图

```mermaid
%%{init: {'theme': 'base', 'themeVariables': { 'primaryColor': '#b5add3', 'primaryTextColor': '#2c3e50', 'primaryBorderColor': '#9e95bb', 'lineColor': '#9e95bb', 'secondaryColor': '#d6c0b0', 'tertiaryColor': '#f9f9f9' }}}%%
graph TB
    Mobile[移动端底座] --> Frontend[前端技术栈]
    Mobile --> DevOps[DevOps技术栈]
    
    subgraph 前端技术栈
        Frontend --> Framework[框架层]
        Frontend --> UI[UI层]
        Frontend --> State[状态管理层]
        Frontend --> Network[网络层]
        
        Framework --> NextJS[Next.js]
        NextJS --> React[React]
        NextJS --> TypeScript[TypeScript]
        
        UI --> AntdMobile[Ant Design Mobile]
        UI --> TailwindCSS[TailwindCSS]
        UI --> StyledComponents[Styled Components]
        
        State --> Zustand[Zustand]
        State --> Immer[Immer.js]
        
        Network --> Axios[Axios]
    end
  
    
    subgraph DevOps技术栈
        DevOps --> Version[版本控制]
        DevOps --> CI[持续集成]
        DevOps --> CD[持续部署]
        DevOps --> Monitoring[监控系统]
        
        Version --> Git[Git]
        
        CI --> GitHub[GitHub Actions]
        CI --> Jest[Jest测试]
        CI --> ESLint[ESLint]
        
        CD --> Vercel[Vercel]
        CD --> Docker[Docker]
        
        Monitoring --> Sentry[Sentry]
        Monitoring --> Analytics[分析工具]
    end
    
    classDef main fill:#b5add3,stroke:#9e95bb,stroke-width:2px,color:#2c3e50;
    classDef frontendStack fill:#a8d1ce,stroke:#89b0ad,stroke-width:2px,color:#2d3e3d;
    classDef backendStack fill:#d6c0b0,stroke:#b7a58f,stroke-width:2px,color:#3e342c;
    classDef devopsStack fill:#e8f0f7,stroke:#a8c0d6,stroke-width:2px,color:#3a4a5c;
    classDef tech fill:#f7e8aa,stroke:#d9cb94,stroke-width:2px,color:#4d452d;
    
    class Mobile main;
    class Frontend,Framework,UI,State,Network frontendStack;
    class Backend,APIGateway,Services,Database,Cache backendStack;
    class DevOps,Version,CI,CD,Monitoring devopsStack;
    class NextJS,React,TypeScript,AntdMobile,TailwindCSS,StyledComponents,Zustand,Immer,Axios,SWR,NextJSAPI,OAuth,WeChat,BusinessAPI,SQL,NoSQL,Redis,LocalCache,Git,GitHub,Jest,ESLint,Vercel,Docker,Sentry,Analytics tech;
```

## 6. 系统交互时序图

```mermaid
%%{init: {'theme': 'base', 'themeVariables': { 'primaryColor': '#b5add3', 'primaryTextColor': '#FFF', 'primaryBorderColor': '#9e95bb', 'lineColor': '#a8d1ce', 'secondaryColor': '#a8d1ce', 'tertiaryColor': '#f9f9f9' }}}%%
sequenceDiagram
    participant User as 用户
    participant App as 移动应用
    participant State as 状态管理
    participant NextJS as Next.js服务
    participant API as API网关
    participant Auth as 认证服务
    participant BizService as 业务服务
    participant DB as 数据库
    
    User->>App: 1. 启动应用
    App->>NextJS: 2. 请求初始页面
    NextJS->>Auth: 3. 检查认证状态
    Auth-->>NextJS: 4. 返回认证结果
    
    alt 未认证
        NextJS-->>App: 5a. 返回登录页面
        App->>User: 6a. 显示登录页面
        User->>App: 7a. 输入凭据
        App->>Auth: 8a. 提交认证请求
        Auth->>DB: 9a. 验证凭据
        DB-->>Auth: 10a. 返回验证结果
        Auth-->>App: 11a. 返回认证结果
    else 已认证
        NextJS->>BizService: 5b. 请求初始数据
        BizService->>DB: 6b. 查询数据
        DB-->>BizService: 7b. 返回数据
        BizService-->>NextJS: 8b. 返回处理后数据
        NextJS-->>App: 9b. 返回渲染页面
    end
    
    App->>State: 12. 初始化状态
    App->>User: 13. 显示主页面
    
    User->>App: 14. 交互操作
    App->>State: 15. 更新本地状态
    App->>API: 16. 发送API请求
    API->>Auth: 17. 验证请求
    Auth-->>API: 18. 通过验证
    API->>BizService: 19. 转发业务请求
    BizService->>DB: 20. 处理数据
    DB-->>BizService: 21. 返回数据
    BizService-->>API: 22. 返回处理结果
    API-->>App: 23. 返回API响应
    App->>State: 24. 更新全局状态
    State-->>App: 25. 通知UI更新
    App->>User: 26. 更新界面展示
```

## 7. 应用派生架构

### 7.1 应用派生关系图

```mermaid
%%{init: {'theme': 'base', 'themeVariables': { 'primaryColor': '#a8c0d6', 'primaryTextColor': '#2c3e50', 'primaryBorderColor': '#6d8cb0', 'lineColor': '#6d8cb0', 'secondaryColor': '#8fb0a9', 'tertiaryColor': '#f9f9f9' }}}%%
graph TB
    subgraph 移动底座平台
        MobilePlatform[移动底座平台] --> CoreModules[核心模块]
        MobilePlatform --> NPMPackage[底座NPM包]
        
        CoreModules --> Auth[认证授权模块]
        CoreModules --> UI[UI组件模块]
        CoreModules --> State[状态管理模块]
        CoreModules --> Routing[路由导航模块]
        CoreModules --> Utility[工具模块]
        CoreModules --> I18N[国际化模块]
        
        Auth --> NPMPackage
        UI --> NPMPackage
        State --> NPMPackage
        Routing --> NPMPackage
        Utility --> NPMPackage
        I18N --> NPMPackage
    end
    
    NPMPackage -->|安装依赖| App1[业务应用1]
    NPMPackage -->|安装依赖| App2[业务应用2]
    NPMPackage -->|安装依赖| App3[业务应用3]
    
    App1 --> App1Business[业务模块1]
    App2 --> App2Business[业务模块2]
    App3 --> App3Business[业务模块3]
    
    subgraph 复用与扩展关系
        Auth -.->|暴露API| App1Business
        UI -.->|组件复用| App1Business
        State -.->|状态逻辑| App1Business
        Routing -.->|路由扩展| App1Business
        Utility -.->|工具函数| App1Business
        I18N -.->|多语言资源| App1Business
        
        Auth -.->|暴露API| App2Business
        UI -.->|组件复用| App2Business
        State -.->|状态逻辑| App2Business
        Routing -.->|路由扩展| App2Business
        Utility -.->|工具函数| App2Business
        I18N -.->|多语言资源| App2Business
        
        Auth -.->|暴露API| App3Business
        UI -.->|组件复用| App3Business
        State -.->|状态逻辑| App3Business
        Routing -.->|路由扩展| App3Business
        Utility -.->|工具函数| App3Business
        I18N -.->|多语言资源| App3Business
    end
    
    classDef platform fill:#b5add3,stroke:#9e95bb,stroke-width:2px,color:#2c3e50;
    classDef core fill:#a8d1ce,stroke:#89b0ad,stroke-width:2px,color:#2d3e3d;
    classDef module fill:#d6c0b0,stroke:#b7a58f,stroke-width:2px,color:#3e342c;
    classDef app fill:#e8f0f7,stroke:#a8c0d6,stroke-width:2px,color:#3a4a5c;
    classDef business fill:#f7e8aa,stroke:#d9cb94,stroke-width:2px,color:#4d452d;
    classDef npm fill:#f9f9f9,stroke:#d9d9d9,stroke-width:2px,color:#2c3e50;
    
    class MobilePlatform platform;
    class CoreModules,Auth,UI,State,Routing,Utility,I18N core;
    class App1,App2,App3 app;
    class App1Business,App2Business,App3Business business;
    class NPMPackage npm;
```

### 7.2 包依赖管理策略图

```mermaid
%%{init: {'theme': 'base', 'themeVariables': { 'primaryColor': '#b5add3', 'primaryTextColor': '#2c3e50', 'primaryBorderColor': '#9e95bb', 'lineColor': '#9e95bb', 'secondaryColor': '#d6c0b0', 'tertiaryColor': '#f9f9f9' }}}%%
graph TB
    subgraph 底座仓库
        Platform[底座代码仓库] --> PlatformDev[底座开发]
        PlatformDev --> |版本控制| GitFlow[Git工作流]
        GitFlow --> |主分支| MainBranch[main分支]
        GitFlow --> |开发分支| DevBranch[develop分支]
        GitFlow --> |功能分支| FeatureBranch[feature分支]
        GitFlow --> |发布分支| ReleaseBranch[release分支]
        
        MainBranch --> |发布| NPMRegistry[NPM注册表]
        NPMRegistry --> |版本1.0.0| Package1[底座包v1.0.0]
        NPMRegistry --> |版本1.1.0| Package2[底座包v1.1.0]
        NPMRegistry --> |版本1.2.0| Package3[底座包v1.2.0]
    end
    
    subgraph 业务应用1
        App1[应用1代码仓库] --> App1Package[package.json]
        App1Package --> |依赖v1.0.0| App1Dev1[应用1初始开发]
        App1Dev1 --> |升级依赖| App1Package2[更新package.json]
        App1Package2 --> |依赖v1.1.0| App1Dev2[应用1功能迭代]
        App1Dev2 --> |升级依赖| App1Package3[更新package.json]
        App1Package3 --> |依赖v1.2.0| App1Dev3[应用1持续开发]
        
        Package1 -.->|安装依赖| App1Package
        Package2 -.->|安装依赖| App1Package2
        Package3 -.->|安装依赖| App1Package3
    end
    
    subgraph 业务应用2
        App2[应用2代码仓库] --> App2Package[package.json]
        App2Package --> |依赖v1.0.0| App2Dev1[应用2初始开发]
        App2Dev1 --> |升级依赖| App2Package2[更新package.json]
        App2Package2 --> |依赖v1.1.0| App2Dev2[应用2功能迭代]
        
        Package1 -.->|安装依赖| App2Package
        Package2 -.->|安装依赖| App2Package2
    end
    
    classDef repository fill:#b5add3,stroke:#9e95bb,stroke-width:2px,color:#2c3e50;
    classDef branch fill:#d6c0b0,stroke:#b7a58f,stroke-width:2px,color:#3e342c;
    classDef package fill:#a8d1ce,stroke:#89b0ad,stroke-width:2px,color:#2d3e3d;
    classDef version fill:#e8f0f7,stroke:#a8c0d6,stroke-width:2px,color:#3a4a5c;
    classDef registry fill:#f7e8aa,stroke:#d9cb94,stroke-width:2px,color:#4d452d;
    
    class Platform,App1,App2 repository;
    class MainBranch,DevBranch,FeatureBranch,ReleaseBranch branch;
    class Package1,Package2,Package3,App1Package,App1Package2,App1Package3,App2Package,App2Package2 package;
    class App1Dev1,App1Dev2,App1Dev3,App2Dev1,App2Dev2,PlatformDev version;
    class NPMRegistry,GitFlow registry;
```

### 7.3 模块继承与扩展图

```mermaid
%%{init: {'theme': 'base', 'themeVariables': { 'primaryColor': '#a8c0d6', 'primaryTextColor': '#2c3e50', 'primaryBorderColor': '#8fa5b7', 'lineColor': '#8fa5b7', 'secondaryColor': '#d6c0b0', 'tertiaryColor': '#f9f9f9' }}}%%
graph TB
    subgraph 移动底座NPM包
        CoreComponents[核心组件] --> BaseUI[基础UI组件]
        CoreComponents --> BaseState[基础状态管理]
        CoreComponents --> BaseRouting[基础路由]
        
        BaseUI --> Button[按钮组件]
        BaseUI --> Form[表单组件]
        BaseUI --> List[列表组件]
        BaseUI --> Modal[模态框组件]
        
        BaseState --> AuthStore[认证状态]
        BaseState --> UIStore[UI状态]
        
        BaseRouting --> Router[路由器]
        BaseRouting --> Guards[路由守卫]
        
        ExtensionPoints[扩展点系统] --> UIExtension[UI扩展点]
        ExtensionPoints --> StateExtension[状态扩展点]
        ExtensionPoints --> RouteExtension[路由扩展点]
        ExtensionPoints --> PluginSystem[插件系统]
        
        NPMExports[NPM包导出] --> ExportedUI[导出的UI]
        NPMExports --> ExportedState[导出的状态]
        NPMExports --> ExportedRoutes[导出的路由]
        NPMExports --> ExportedExtensions[导出的扩展点]
        
        CoreComponents --> NPMExports
        ExtensionPoints --> NPMExports
    end
    
    subgraph 业务应用1
        App1[应用1项目] --> ImportUI1[导入底座UI]
        App1 --> ImportState1[导入底座状态]
        App1 --> ImportRoutes1[导入底座路由]
        App1 --> App1Config[应用1配置]
        
        ImportUI1 -.->|使用| ExportedUI
        ImportState1 -.->|使用| ExportedState
        ImportRoutes1 -.->|使用| ExportedRoutes
        
        App1Config -->|配置| App1Business[业务模块1]
        App1Business -->|使用| App1Extensions[应用1扩展]
        App1Extensions -.->|接入| ExportedExtensions
        
        App1Business --> CustomForm1[定制表单1]
        App1Business --> CustomList1[定制列表1]
        CustomForm1 -.->|基于| Form
        CustomList1 -.->|基于| List
    end
    
    subgraph 业务应用2
        App2[应用2项目] --> ImportUI2[导入底座UI]
        App2 --> ImportState2[导入底座状态]
        App2 --> ImportRoutes2[导入底座路由]
        App2 --> App2Config[应用2配置]
        
        ImportUI2 -.->|使用| ExportedUI
        ImportState2 -.->|使用| ExportedState
        ImportRoutes2 -.->|使用| ExportedRoutes
        
        App2Config -->|配置| App2Business[业务模块2]
        App2Business -->|使用| App2Extensions[应用2扩展]
        App2Extensions -.->|接入| ExportedExtensions
        
        App2Business --> CustomForm2[定制表单2]
        App2Business --> CustomList2[定制列表2]
        CustomForm2 -.->|基于| Form
        CustomList2 -.->|基于| List
    end
    
    classDef platform fill:#b5add3,stroke:#9e95bb,stroke-width:2px,color:#2c3e50;
    classDef core fill:#a8d1ce,stroke:#89b0ad,stroke-width:2px,color:#2d3e3d;
    classDef baseComponent fill:#d6c0b0,stroke:#b7a58f,stroke-width:2px,color:#3e342c;
    classDef extension fill:#f9f9f9,stroke:#d9d9d9,stroke-width:2px,color:#2c3e50;
    classDef app fill:#e8f0f7,stroke:#a8c0d6,stroke-width:2px,color:#3a4a5c;
    classDef business fill:#f7e8aa,stroke:#d9cb94,stroke-width:2px,color:#4d452d;
    classDef npm fill:#cae3ca,stroke:#97c397,stroke-width:2px,color:#2e3e2e;
    
    class 移动底座NPM包,CoreComponents,ExtensionPoints,NPMExports platform;
    class BaseUI,BaseState,BaseRouting,ExportedUI,ExportedState,ExportedRoutes,ExportedExtensions core;
    class Button,Form,List,Modal,AuthStore,UIStore,Router,Guards baseComponent;
    class UIExtension,StateExtension,RouteExtension,PluginSystem extension;
    class App1,App1Config,ImportUI1,ImportState1,ImportRoutes1,App2,App2Config,ImportUI2,ImportState2,ImportRoutes2 app;
    class App1Business,App1Extensions,CustomForm1,CustomList1,App2Business,App2Extensions,CustomForm2,CustomList2 business;
```

### 7.4 开发流程图

```mermaid
%%{init: {'theme': 'base', 'themeVariables': { 'primaryColor': '#b5add3', 'primaryTextColor': '#a8d1ce', 'primaryBorderColor': '#9e95bb', 'lineColor': '#a8d1ce', 'secondaryColor': '#a8d1ce', 'tertiaryColor': '#f9f9f9' }}}%%
sequenceDiagram
    participant P as 平台开发团队
    participant R as NPM注册表
    participant A1 as 业务应用1团队
    participant A1R as 应用1代码库
    participant A2 as 业务应用2团队
    participant A2R as 应用2代码库
    
    P->>R: 1. 开发底座v1.0.0
    P->>R: 2. 发布到NPM注册表
    Note over R: @mobile-base/core@1.0.0
    
    A1->>R: 3. npm install @mobile-base/core@1.0.0
    A1->>A1R: 4. 初始化业务应用1代码库
    
    A2->>R: 5. npm install @mobile-base/core@1.0.0
    A2->>A2R: 6. 初始化业务应用2代码库
    
    A1->>A1R: 7. 基于底座开发业务应用1
    A2->>A2R: 8. 基于底座开发业务应用2
    
    P->>R: 9. 底座功能迭代/Bug修复
    P->>R: 10. 发布底座新版本
    Note over R: @mobile-base/core@1.1.0
    
    A1->>R: 11. npm update @mobile-base/core --to=1.1.0
    A1->>A1R: 12. 更新package.json依赖
    Note over A1R: 升级适配测试
    
    A2->>R: 13. npm update @mobile-base/core --to=1.1.0
    A2->>A2R: 14. 更新package.json依赖
    Note over A2R: 升级适配测试
    
    A1->>A1R: 15. 继续业务应用1开发
    A2->>A2R: 16. 继续业务应用2开发
    
    P->>R: 17. 发布底座补丁版本
    Note over R: @mobile-base/core@1.1.1
    
    A1->>R: 18. npm update @mobile-base/core
    A1->>A1R: 19. 应用1采用补丁版本
    
    Note over P,A2R: 底座与业务应用并行开发，通过NPM包管理依赖
```

### 7.5 扩展点与插件机制图

```mermaid
%%{init: {'theme': 'base', 'themeVariables': { 'primaryColor': '#b5add3', 'primaryTextColor': '#2c3e50', 'primaryBorderColor': '#9e95bb', 'lineColor': '#9e95bb', 'secondaryColor': '#d6c0b0', 'tertiaryColor': '#f9f9f9' }}}%%
graph TB
    Platform[底座平台核心] --> PluginSystem[插件系统]
    
    PluginSystem --> BuildTimeHooks[构建期钩子]
    PluginSystem --> RunTimeHooks[运行期钩子]
    PluginSystem --> PluginRegistry[插件注册表]
    PluginSystem --> PluginLoader[插件加载器]
    PluginSystem --> PluginAPI[插件API]

    BuildTimeHooks --> WebpackConfigHook[Webpack配置钩子]
    BuildTimeHooks --> BabelConfigHook[Babel配置钩子]
    BuildTimeHooks --> PostCSSConfigHook[PostCSS配置钩子]
    BuildTimeHooks --> StaticAssetsHook[静态资源处理钩子]

    RunTimeHooks --> RequestHook[请求钩子]
    RunTimeHooks --> ResponseHook[响应钩子]
    RunTimeHooks --> RouteGuardHook[路由守卫钩子]
    RunTimeHooks --> StateMutationHook[状态变更钩子]
    RunTimeHooks --> UIComponentFilterHook[UI组件渲染过滤钩子]
    RunTimeHooks --> AppLifecycleHook[应用生命周期钩子]

    subgraph 业务应用插件
        App1Plugins[应用1插件集]
        App1Plugins --> App1BuildPlugin[应用1 构建期插件]
        App1Plugins --> App1RuntimePlugin[应用1 运行期插件]
        
        App2Plugins[应用2插件集]
        App2Plugins --> App2BuildPlugin[应用2 构建期插件]
        App2Plugins --> App2RuntimePlugin[应用2 运行期插件]
    end
    
    App1BuildPlugin -.->|注入逻辑| WebpackConfigHook
    App1BuildPlugin -.->|注入逻辑| StaticAssetsHook
    App1RuntimePlugin -.->|注入逻辑| RequestHook
    App1RuntimePlugin -.->|注入逻辑| RouteGuardHook
    
    App2BuildPlugin -.->|注入逻辑| BabelConfigHook
    App2RuntimePlugin -.->|注入逻辑| StateMutationHook
    App2RuntimePlugin -.->|注入逻辑| UIComponentFilterHook
    
    PluginRegistry -.->|管理| App1Plugins
    PluginRegistry -.->|管理| App2Plugins
    PluginLoader -->|加载| App1Plugins
    PluginLoader -->|加载| App2Plugins
    
    classDef core fill:#b5add3,stroke:#9e95bb,stroke-width:2px,color:#2c3e50;
    classDef hooks fill:#a8d1ce,stroke:#89b0ad,stroke-width:2px,color:#2d3e3d;
    classDef hookInstance fill:#d6c0b0,stroke:#b7a58f,stroke-width:2px,color:#3e342c;
    classDef pluginCollection fill:#e8f0f7,stroke:#a8c0d6,stroke-width:2px,color:#3a4a5c;
    classDef businessPlugin fill:#f7e8aa,stroke:#d9cb94,stroke-width:2px,color:#4d452d;
    
    class Platform,PluginSystem,PluginRegistry,PluginLoader,PluginAPI core;
    class BuildTimeHooks,RunTimeHooks hooks;
    class WebpackConfigHook,BabelConfigHook,PostCSSConfigHook,StaticAssetsHook,RequestHook,ResponseHook,RouteGuardHook,StateMutationHook,UIComponentFilterHook,AppLifecycleHook hookInstance;
    class App1Plugins,App2Plugins pluginCollection;
    class App1BuildPlugin,App1RuntimePlugin,App2BuildPlugin,App2RuntimePlugin businessPlugin;
```

## 总结

移动端底座系统架构涵盖了前端到后端的完整技术体系：

1. **系统全景架构**展示了整体系统组成，包括客户端、Next.js服务集群、后端服务和基础设施等核心组件及其交互关系。

2. **系统逻辑分层架构**遵循经典的分层设计，清晰划分了表现层、业务逻辑层、数据访问层和基础设施层，每层具有明确的职责和边界。

3. **核心模块架构**详细描述了系统的六大核心模块：认证授权、UI组件、状态管理、路由导航、国际化和工具模块，及其内部组成。

4. **部署架构**展示了从开发到生产的完整流程，包括CI/CD流水线、多环境部署策略和生产环境的基础设施配置。

5. **技术栈架构**全面呈现了系统采用的技术选型，覆盖前端、后端和DevOps三大领域，确保技术的一致性和先进性。

6. **系统交互时序图**通过典型用例展示了系统各组件间的交互流程，清晰描述了数据流动和状态变化的过程。

7. **应用派生架构**详细阐述了底座与业务应用之间的关系：
   - **派生关系图**展示了底座核心模块如何打包为NPM包并被业务应用引入，实现"一次开发，多处使用"
   - **包依赖管理策略**描述了底座与业务应用之间基于NPM包的版本管理和更新机制
   - **模块继承与扩展**说明了业务应用如何基于底座组件进行复用和定制开发
   - **开发流程**展示了平台团队与业务应用团队的协同工作模式
   - **扩展点与插件机制**提供了业务应用在不修改底座核心代码的情况下实现定制化需求的方法

这一架构设计确保了系统的高性能、可扩展性、可维护性和安全性，为移动应用开发提供了坚实的技术基础。同时，通过底座与业务应用的分离，实现了"共性依赖，个性扩展"的技术治理目标，提高了开发效率和代码复用率。
