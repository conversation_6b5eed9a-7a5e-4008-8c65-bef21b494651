# 📦 移动端底座框架发包指南

## 🎯 发包准备工作总结

### ✅ 已完成的工作

#### 1. **package.json 配置完善**
- ✅ 包名：`@cvte/mobile-framework`
- ✅ 版本：`0.1.0`
- ✅ 描述和关键词
- ✅ 入口文件配置（main, module, types）
- ✅ exports 字段配置（支持子模块导入）
- ✅ files 字段（指定发布文件）
- ✅ peerDependencies 配置
- ✅ 构建和发布脚本

#### 2. **构建系统配置**
- ✅ Rollup 构建配置
- ✅ TypeScript 编译配置
- ✅ 多格式输出（CJS + ESM）
- ✅ 类型声明文件生成
- ✅ CSS 样式处理
- ✅ 源码映射生成

#### 3. **模块化架构**
- ✅ 主入口文件（src/index.ts）
- ✅ 子模块入口（components, lib, hooks, utils）
- ✅ 核心功能模块（API客户端、缓存、日志、状态管理）
- ✅ 插件系统基础
- ✅ 样式系统

#### 4. **业务端集成支持**
- ✅ 初始化脚本（postinstall.js）
- ✅ 框架提供者组件
- ✅ 应用配置系统
- ✅ 插件化配置机制

#### 5. **开发工具**
- ✅ 发包前检查脚本
- ✅ 代码规范配置
- ✅ 构建验证
- ✅ 文档完善

## 🚀 发布流程

### 第一步：最终检查

```bash
# 1. 运行发包前检查
pnpm run pre-publish

# 2. 确保所有测试通过
pnpm run lint
pnpm run type-check

# 3. 清理并重新构建
pnpm run clean:dist
pnpm run build:package
```

### 第二步：版本管理

```bash
# 补丁版本（bug修复）
npm version patch

# 次要版本（新功能）
npm version minor

# 主要版本（破坏性更改）
npm version major
```

### 第三步：发布到NPM

```bash
# 发布正式版本
npm publish

# 发布测试版本
npm publish --tag beta

# 发布到私有仓库
npm publish --registry https://your-private-registry.com
```

## 📋 业务端使用指南

### 安装

```bash
npm install @cvte/mobile-framework
```

### 基础使用

```typescript
// app/layout.tsx
import { MobileFrameworkProvider, createMobileApp } from '@cvte/mobile-framework';
import '@cvte/mobile-framework/styles';

const app = createMobileApp({
  appName: '我的移动应用',
  version: '1.0.0',
  appDir: './src/app',
  apiBaseUrl: process.env.NEXT_PUBLIC_API_BASE_URL || '/api',
  theme: {
    mode: 'auto',
  },
  logger: {
    level: 'info',
    enableConsole: true,
  },
});

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="zh-CN">
      <body>
        <MobileFrameworkProvider app={app}>
          {children}
        </MobileFrameworkProvider>
      </body>
    </html>
  );
}
```

### 模块化导入

```typescript
// 导入核心库
import { apiClient, logger, cacheManager } from '@cvte/mobile-framework/lib';

// 导入组件
import { Button, Modal } from '@cvte/mobile-framework/components';

// 导入Hooks
import { useLocalStorage, useDebounce } from '@cvte/mobile-framework/hooks';

// 导入工具函数
import { formatDateTime, getDeviceInfo } from '@cvte/mobile-framework/utils';
```

## 🎯 插件化配置

### 获取业务端配置

```typescript
const app = createMobileApp({
  appName: '业务应用',
  appDir: './src/app', // Next.js App Router目录
  apiBaseUrl: process.env.NEXT_PUBLIC_API_BASE_URL,
  plugins: [
    {
      name: 'analytics',
      config: {
        trackingId: 'GA-XXXXXXX',
      }
    }
  ]
});

// 在框架内部获取配置
const appDir = app.getAppDir();
const apiBaseUrl = app.getApiBaseUrl();
const config = app.getConfig();
```

### 启动时初始化

框架会在启动时自动：
1. 初始化日志系统
2. 配置API客户端
3. 设置主题系统
4. 加载插件
5. 设置全局错误处理

## 📊 包大小优化

当前构建产物：
- 主包：~50KB (gzipped)
- 子模块按需加载
- Tree-shaking 支持
- 外部依赖排除

## 🔄 版本更新策略

### 语义化版本控制
- **MAJOR**: 破坏性API更改
- **MINOR**: 向后兼容的新功能
- **PATCH**: 向后兼容的bug修复

### 更新通知
- 在README中维护更新日志
- 使用GitHub Releases发布说明
- 重大更新提供迁移指南

## 🛠️ 故障排除

### 常见问题

1. **React版本兼容性**
   - 当前支持React 18+
   - 使用peerDependencies避免版本冲突

2. **Next.js版本兼容性**
   - 当前支持Next.js 14+
   - App Router架构

3. **TypeScript支持**
   - 完整的类型定义
   - 严格模式兼容

### 调试技巧

```typescript
// 启用调试日志
const app = createMobileApp({
  logger: {
    level: 'debug',
    enableConsole: true,
  },
});
```

## 📞 支持与反馈

- **文档**: README.md
- **问题反馈**: GitHub Issues
- **功能请求**: GitHub Discussions
- **技术支持**: 内部技术群

---

**🎉 恭喜！移动端底座框架已准备就绪，可以安全发布！**
