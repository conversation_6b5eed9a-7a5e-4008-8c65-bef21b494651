# 移动端底座项目

基于Next.js 15构建的移动端底座项目，将打包成npm包的方式提供给业务端使用。实现高效的代码组织、组件复用和工程化能力。

## 项目概述

本项目遵循"先搭框架，后填内容"的原则，优先构建系统基础设施和核心功能，然后逐步完善业务模块和UI模板。

## 技术栈

### 核心框架
- **Next.js 15.3.2** - React全栈框架，支持SSR/SSG
- **React 19.0.0** - 最新版本的React
- **TypeScript 5** - 类型安全的JavaScript

### UI组件库
- **Ant Design Mobile 5.39.0** - 移动端UI组件库
- **TailwindCSS 4** - 原子化CSS框架
- **Ant Design Mobile Icons** - 图标库

### 状态管理
- **Zustand 5.0.4** - 轻量级状态管理库

### 工具库
- **es-toolkit 1.38.0** - 现代化工具函数库
- **axios 1.9.0** - HTTP客户端
- **uuid** - 唯一ID生成

### 开发工具
- **ESLint** - 代码检查
- **Prettier** - 代码格式化
- **pnpm** - 包管理器

## 项目结构

```
src/
├── app/                    # Next.js App Router
│   ├── api/               # API路由
│   ├── globals.css        # 全局样式
│   ├── layout.tsx         # 根布局
│   └── page.tsx           # 首页
├── components/            # 组件库
│   ├── ui/               # 基础UI组件
│   ├── layout/           # 布局组件
│   ├── forms/            # 表单组件
│   ├── charts/           # 图表组件
│   └── business/         # 业务组件
├── lib/                  # 核心库
│   ├── api/              # API客户端
│   ├── cache/            # 缓存管理
│   ├── logger/           # 日志系统
│   ├── auth/             # 认证模块
│   └── router/           # 路由管理
├── hooks/                # 自定义Hooks
├── stores/               # 状态管理
├── types/                # 类型定义
├── utils/                # 工具函数
├── constants/            # 常量定义
└── styles/               # 样式文件
```

## 核心功能

### 🚀 已完成功能（第一阶段）

#### 1. 工作空间初始化
- ✅ Next.js 15框架集成
- ✅ TypeScript配置
- ✅ 路径别名配置
- ✅ 目录结构规划

#### 2. 技术栈集成
- ✅ Ant Design Mobile配置
- ✅ TailwindCSS集成
- ✅ Zustand状态管理
- ✅ es-toolkit工具库
- ✅ Axios网络请求
- ✅ ESLint + Prettier代码规范

#### 3. 核心基础设施
- ✅ 网络请求层（基于Axios）
  - 统一的API客户端
  - 请求/响应拦截器
  - 错误处理机制
  - 加载状态管理
  - 认证token自动添加
- ✅ 日志系统
  - 多级别日志记录
  - 控制台输出
  - 本地存储
  - 远程上报支持
- ✅ 缓存机制
  - 内存缓存
  - 持久化缓存
  - TTL过期机制
  - LRU淘汰策略
- ✅ 状态管理系统
  - 全局应用状态
  - 用户认证状态
  - UI状态管理
  - 持久化支持
- ✅ 路由系统
  - 路由守卫
  - 权限检查
  - 面包屑导航
- ✅ 工具函数库
  - 基于es-toolkit的现代工具函数
  - 表单验证
  - 文件处理
  - 设备检测
  - 重试机制

#### 4. 应用配置
- ✅ 多语言支持（中文）
- ✅ 网络状态监听
- ✅ 主题配置
- ✅ 全局提供者组件

## 快速开始

### 环境要求
- Node.js >= 18
- pnpm >= 8

### 安装依赖
```bash
pnpm install
```

### 开发模式
```bash
pnpm run dev
```

### 构建项目
```bash
pnpm run build
```

### 代码检查
```bash
pnpm run lint
pnpm run lint:fix
```

### 代码格式化
```bash
pnpm run format
pnpm run format:check
```

### 类型检查
```bash
pnpm run type-check
```

## 开发规范

### 代码规范
- 使用TypeScript进行类型安全开发
- 遵循ESLint规则
- 使用Prettier进行代码格式化
- 组件和函数必须添加详细注释

### 目录规范
- 组件按功能分类存放
- 使用路径别名简化导入
- 保持目录结构清晰

### 命名规范
- 组件使用PascalCase
- 函数和变量使用camelCase
- 常量使用UPPER_SNAKE_CASE
- 文件名使用kebab-case

## 📦 NPM包使用

### 安装

```bash
# 安装正式版本
npm install @cvte/mobile-framework

# 安装测试版本
npm install @cvte/mobile-framework@beta

# 或使用其他包管理器
yarn add @cvte/mobile-framework@beta
pnpm add @cvte/mobile-framework@beta
```

### 快速开始

#### 方法一：自动初始化（推荐）

安装后会自动运行初始化脚本，为您的项目创建基础结构。如果没有自动运行，可以手动执行：

```bash
# 手动运行初始化
npx mobile-framework-init
```

#### 方法二：手动创建

如果自动初始化不工作，请参考 [QUICK_START.md](./QUICK_START.md) 手动创建文件。

```typescript
// app/layout.tsx
import { MobileFrameworkProvider, createMobileApp } from '@cvte/mobile-framework';
import '@cvte/mobile-framework/styles';

const app = createMobileApp({
  appName: '我的移动应用',
  version: '1.0.0',
  apiBaseUrl: '/api',
});

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="zh-CN">
      <body>
        <MobileFrameworkProvider app={app}>
          {children}
        </MobileFrameworkProvider>
      </body>
    </html>
  );
}
```

### 模块化导入

```typescript
// 导入特定模块
import { apiClient } from '@cvte/mobile-framework/lib';
import { Button } from '@cvte/mobile-framework/components';
import { useLocalStorage } from '@cvte/mobile-framework/hooks';
import { formatDateTime } from '@cvte/mobile-framework/utils';
```

## 🔧 开发与构建

### 开发环境

```bash
# 启动开发服务器
pnpm run dev

# 代码检查
pnpm run lint

# 代码格式化
pnpm run format
```

### 构建NPM包

```bash
# 构建库文件
pnpm run build:package

# 清理构建文件
pnpm run clean:dist
```

### 发布流程

```bash
# 1. 更新版本号
npm version patch|minor|major

# 2. 构建包
pnpm run build:package

# 3. 发布到NPM
npm publish
```

## 🎯 插件化配置

框架支持插件化配置，业务端可以通过配置获取Next.js的appDir等信息：

```typescript
const app = createMobileApp({
  appName: '业务应用',
  appDir: './src/app', // Next.js App Router目录
  apiBaseUrl: process.env.NEXT_PUBLIC_API_BASE_URL,
  plugins: [
    {
      name: 'custom-plugin',
      config: {
        // 插件配置
      }
    }
  ]
});

// 获取配置
const appDir = app.getAppDir();
const apiBaseUrl = app.getApiBaseUrl();
```

## 📋 发包检查清单

### ✅ 已完成
- [x] package.json配置完整（main, module, types, exports等）
- [x] 构建配置（Rollup + TypeScript）
- [x] 入口文件和模块导出
- [x] 初始化脚本（postinstall）
- [x] 类型声明文件生成
- [x] 样式文件处理
- [x] 插件化配置系统
- [x] 文档和使用说明

### 📝 发包前准备
1. **检查版本号**：确认package.json中的版本号
2. **测试构建**：运行`pnpm run build:package`确保构建成功
3. **检查文件**：确认dist目录包含所有必要文件
4. **测试安装**：在测试项目中安装并验证功能
5. **更新文档**：确保README.md和文档是最新的

### 🚀 发布命令
```bash
# 发布到NPM
npm publish

# 发布beta版本
npm publish --tag beta

# 发布到私有仓库
npm publish --registry https://your-private-registry.com
```

## 下一步计划

### 第二阶段：核心框架与应用外壳
- [ ] 基础布局组件
- [ ] 导航系统
- [ ] 认证与授权框架
- [ ] 主题系统

### 第三阶段：基础组件库开发
- [ ] 基础UI组件
- [ ] 用户反馈组件
- [ ] 公共服务组件

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。
