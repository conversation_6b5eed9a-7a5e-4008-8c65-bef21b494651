/**
 * API相关常量
 */
export const API_CONSTANTS = {
  // 请求超时时间
  TIMEOUT: 10000,
  
  // 重试次数
  RETRY_COUNT: 3,
  
  // 状态码
  STATUS_CODES: {
    SUCCESS: 200,
    CREATED: 201,
    NO_CONTENT: 204,
    BAD_REQUEST: 400,
    UNAUTHORIZED: 401,
    FORBIDDEN: 403,
    NOT_FOUND: 404,
    INTERNAL_SERVER_ERROR: 500,
  },
  
  // 响应消息
  MESSAGES: {
    SUCCESS: '操作成功',
    FAILED: '操作失败',
    NETWORK_ERROR: '网络连接失败',
    TIMEOUT: '请求超时',
    UNAUTHORIZED: '未授权，请重新登录',
    FORBIDDEN: '拒绝访问',
    NOT_FOUND: '资源不存在',
    SERVER_ERROR: '服务器内部错误',
  },
} as const;

/**
 * 缓存相关常量
 */
export const CACHE_CONSTANTS = {
  // 缓存键前缀
  KEYS: {
    USER: 'user_',
    API: 'api_',
    CONFIG: 'config_',
    TEMP: 'temp_',
  },
  
  // 缓存时间（毫秒）
  TTL: {
    SHORT: 5 * 60 * 1000, // 5分钟
    MEDIUM: 30 * 60 * 1000, // 30分钟
    LONG: 2 * 60 * 60 * 1000, // 2小时
    VERY_LONG: 24 * 60 * 60 * 1000, // 24小时
  },
} as const;

/**
 * 存储相关常量
 */
export const STORAGE_CONSTANTS = {
  // 本地存储键
  KEYS: {
    AUTH_TOKEN: 'auth_token',
    USER_INFO: 'user_info',
    THEME: 'theme',
    LANGUAGE: 'language',
    SETTINGS: 'settings',
    CACHE: 'cache_',
    LOGS: 'app_logs',
  },
} as const;

/**
 * 路由相关常量
 */
export const ROUTE_CONSTANTS = {
  // 公共路由（无需认证）
  PUBLIC_ROUTES: [
    '/',
    '/login',
    '/register',
    '/forgot-password',
    '/reset-password',
  ],
  
  // 认证路由（需要登录）
  AUTH_ROUTES: [
    '/dashboard',
    '/profile',
    '/settings',
  ],
  
  // 管理员路由（需要管理员权限）
  ADMIN_ROUTES: [
    '/admin',
    '/admin/users',
    '/admin/settings',
  ],
} as const;

/**
 * UI相关常量
 */
export const UI_CONSTANTS = {
  // 断点
  BREAKPOINTS: {
    XS: 480,
    SM: 576,
    MD: 768,
    LG: 992,
    XL: 1200,
    XXL: 1600,
  },
  
  // 动画时间
  ANIMATION: {
    FAST: 150,
    NORMAL: 300,
    SLOW: 500,
  },
  
  // Z-index层级
  Z_INDEX: {
    DROPDOWN: 1000,
    STICKY: 1020,
    FIXED: 1030,
    MODAL_BACKDROP: 1040,
    MODAL: 1050,
    POPOVER: 1060,
    TOOLTIP: 1070,
    TOAST: 1080,
  },
  
  // 颜色主题
  COLORS: {
    PRIMARY: '#1890ff',
    SUCCESS: '#52c41a',
    WARNING: '#faad14',
    ERROR: '#ff4d4f',
    INFO: '#1890ff',
  },
} as const;

/**
 * 表单相关常量
 */
export const FORM_CONSTANTS = {
  // 验证规则
  VALIDATION: {
    EMAIL_REGEX: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    PHONE_REGEX: /^1[3-9]\d{9}$/,
    ID_CARD_REGEX: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/,
    PASSWORD_MIN_LENGTH: 6,
    PASSWORD_MAX_LENGTH: 20,
  },
  
  // 错误消息
  ERROR_MESSAGES: {
    REQUIRED: '此字段为必填项',
    EMAIL_INVALID: '请输入有效的邮箱地址',
    PHONE_INVALID: '请输入有效的手机号码',
    PASSWORD_TOO_SHORT: '密码长度不能少于6位',
    PASSWORD_TOO_LONG: '密码长度不能超过20位',
    CONFIRM_PASSWORD_MISMATCH: '两次输入的密码不一致',
  },
} as const;

/**
 * 文件相关常量
 */
export const FILE_CONSTANTS = {
  // 文件大小限制（字节）
  MAX_SIZE: {
    IMAGE: 5 * 1024 * 1024, // 5MB
    DOCUMENT: 10 * 1024 * 1024, // 10MB
    VIDEO: 100 * 1024 * 1024, // 100MB
  },
  
  // 支持的文件类型
  ALLOWED_TYPES: {
    IMAGE: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
    DOCUMENT: ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
    EXCEL: ['application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'],
  },
  
  // 文件扩展名
  EXTENSIONS: {
    IMAGE: ['.jpg', '.jpeg', '.png', '.gif', '.webp'],
    DOCUMENT: ['.pdf', '.doc', '.docx'],
    EXCEL: ['.xls', '.xlsx'],
  },
} as const;

/**
 * 业务相关常量
 */
export const BUSINESS_CONSTANTS = {
  // 分页
  PAGINATION: {
    DEFAULT_PAGE_SIZE: 10,
    PAGE_SIZE_OPTIONS: [10, 20, 50, 100],
    MAX_PAGE_SIZE: 100,
  },
  
  // 状态
  STATUS: {
    ACTIVE: 'active',
    INACTIVE: 'inactive',
    PENDING: 'pending',
    APPROVED: 'approved',
    REJECTED: 'rejected',
    DRAFT: 'draft',
    PUBLISHED: 'published',
  },
  
  // 优先级
  PRIORITY: {
    LOW: 'low',
    MEDIUM: 'medium',
    HIGH: 'high',
    URGENT: 'urgent',
  },
} as const;

/**
 * 环境相关常量
 */
export const ENV_CONSTANTS = {
  NODE_ENV: process.env.NODE_ENV || 'development',
  IS_DEVELOPMENT: process.env.NODE_ENV === 'development',
  IS_PRODUCTION: process.env.NODE_ENV === 'production',
  IS_TEST: process.env.NODE_ENV === 'test',
  
  // API地址
  API_BASE_URL: process.env.NEXT_PUBLIC_API_BASE_URL || '/api',
  
  // 其他配置
  APP_NAME: process.env.NEXT_PUBLIC_APP_NAME || '移动端应用',
  APP_VERSION: process.env.NEXT_PUBLIC_APP_VERSION || '1.0.0',
} as const;
