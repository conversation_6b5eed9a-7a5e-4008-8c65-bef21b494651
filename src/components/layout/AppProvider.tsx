'use client';

import React, { useEffect } from 'react';
import { ConfigProvider } from 'antd-mobile';
import { useAppStore } from '@/stores';
import { logger } from '@/lib/logger';

/**
 * 应用程序提供者组件
 * 负责全局状态管理、主题配置、网络状态监听等
 */
export function AppProvider({ children }: { children: React.ReactNode }) {
  const { setOnlineStatus } = useAppStore();

  useEffect(() => {
    // 监听网络状态变化
    const handleOnline = () => {
      setOnlineStatus(true);
      logger.info('Network status: online');
    };

    const handleOffline = () => {
      setOnlineStatus(false);
      logger.warn('Network status: offline');
    };

    // 添加事件监听器
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // 初始化网络状态
    setOnlineStatus(navigator.onLine);

    // 清理事件监听器
    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, [setOnlineStatus]);

  return (
    <ConfigProvider
      locale={{
        locale: 'zh-CN',
        common: {
          confirm: '确认',
          cancel: '取消',
          loading: '加载中...',
          close: '关闭',
        },
        Calendar: {
          title: '日历',
          confirm: '确认',
          start: '开始',
          end: '结束',
          today: '今天',
          markItems: ['一', '二', '三', '四', '五', '六', '日'],
          yearAndMonth: '年月',
        },
        Cascader: {
          placeholder: '请选择',
        },
        Dialog: {
          ok: '我知道了',
        },
        DatePicker: {
          tillNow: '至今',
        },
        ErrorBlock: {
          default: {
            title: '页面遇到一些小问题',
            description: '待会来试试',
          },
          busy: {
            title: '前方拥堵',
            description: '刷新试试',
          },
          disconnected: {
            title: '网络有点忙',
            description: '动动手指帮忙修复',
          },
          empty: {
            title: '没有找到你需要的东西',
            description: '找找其他的吧',
          },
        },
        Form: {
          required: '必填',
          optional: '选填',
          defaultValidateMessages: {
            default: '字段验证错误',
            required: '请输入${label}',
            enum: '${label}必须是其中一个[${enum}]',
            whitespace: '${label}不能为空字符',
            date: {
              format: '${label}日期格式无效',
              parse: '${label}不能转换为日期',
              invalid: '${label}是一个无效日期',
            },
            types: {
              string: '${label}不是一个有效的字符串',
              method: '${label}不是一个有效的方法',
              array: '${label}不是一个有效的数组',
              object: '${label}不是一个有效的对象',
              number: '${label}不是一个有效的数字',
              date: '${label}不是一个有效的日期',
              boolean: '${label}不是一个有效的布尔值',
              integer: '${label}不是一个有效的整数',
              float: '${label}不是一个有效的浮点数',
              regexp: '${label}不是一个有效的正则表达式',
              email: '${label}不是一个有效的邮箱',
              url: '${label}不是一个有效的URL',
              hex: '${label}不是一个有效的十六进制',
            },
            string: {
              len: '${label}须为${len}个字符',
              min: '${label}最少${min}个字符',
              max: '${label}最多${max}个字符',
              range: '${label}须在${min}-${max}字符之间',
            },
            number: {
              len: '${label}必须等于${len}',
              min: '${label}最小值为${min}',
              max: '${label}最大值为${max}',
              range: '${label}须在${min}-${max}之间',
            },
            array: {
              len: '须为${len}个${label}',
              min: '最少${min}个${label}',
              max: '最多${max}个${label}',
              range: '${label}数量须在${min}-${max}之间',
            },
            pattern: {
              mismatch: '${label}与模式不匹配${pattern}',
            },
          },
        },
        ImageUploader: {
          uploading: '上传中...',
          upload: '上传',
        },
        InfiniteScroll: {
          noMore: '没有更多了',
          failedToLoad: '加载失败',
          retry: '重新加载',
        },
        Input: {
          clear: '清除',
        },
        Mask: {
          name: '遮罩层',
        },
        Modal: {
          ok: '我知道了',
        },
        PasscodeInput: {
          name: '密码输入框',
        },
        PullToRefresh: {
          pulling: '下拉刷新',
          canRelease: '释放立即刷新',
          complete: '刷新成功',
        },
        SearchBar: {
          name: '搜索框',
        },
        Slider: {
          name: '滑动输入条',
        },
        Stepper: {
          decrease: '减少',
          increase: '增加',
        },
        Switch: {
          name: '开关',
        },
        Selector: {
          name: '选择组',
        },
      }}
    >
      {children}
    </ConfigProvider>
  );
}
