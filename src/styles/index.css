/**
 * @cvte/mobile-framework 样式入口文件
 * 包含框架的基础样式和主题变量
 */

/* 导入TailwindCSS */
@import "tailwindcss";

/* 框架基础变量 */
:root {
  /* 颜色系统 */
  --mf-primary: #1677ff;
  --mf-primary-hover: #4096ff;
  --mf-primary-active: #0958d9;
  
  --mf-success: #52c41a;
  --mf-warning: #faad14;
  --mf-error: #ff4d4f;
  --mf-info: #1677ff;
  
  /* 中性色 */
  --mf-text-primary: #000000d9;
  --mf-text-secondary: #00000073;
  --mf-text-tertiary: #00000040;
  --mf-text-quaternary: #00000026;
  
  --mf-bg-primary: #ffffff;
  --mf-bg-secondary: #fafafa;
  --mf-bg-tertiary: #f5f5f5;
  
  --mf-border-color: #d9d9d9;
  --mf-border-color-split: #f0f0f0;
  
  /* 间距系统 */
  --mf-spacing-xs: 4px;
  --mf-spacing-sm: 8px;
  --mf-spacing-md: 16px;
  --mf-spacing-lg: 24px;
  --mf-spacing-xl: 32px;
  
  /* 圆角 */
  --mf-border-radius-sm: 4px;
  --mf-border-radius-md: 6px;
  --mf-border-radius-lg: 8px;
  
  /* 阴影 */
  --mf-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.03), 0 1px 6px -1px rgba(0, 0, 0, 0.02), 0 2px 4px 0 rgba(0, 0, 0, 0.02);
  --mf-shadow-md: 0 1px 2px 0 rgba(0, 0, 0, 0.03), 0 1px 6px -1px rgba(0, 0, 0, 0.02), 0 2px 4px 0 rgba(0, 0, 0, 0.02);
  --mf-shadow-lg: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  
  /* 字体 */
  --mf-font-size-xs: 10px;
  --mf-font-size-sm: 12px;
  --mf-font-size-md: 14px;
  --mf-font-size-lg: 16px;
  --mf-font-size-xl: 18px;
  --mf-font-size-xxl: 20px;
  
  --mf-line-height-sm: 1.2;
  --mf-line-height-md: 1.4;
  --mf-line-height-lg: 1.6;
  
  /* Z-index层级 */
  --mf-z-index-dropdown: 1000;
  --mf-z-index-sticky: 1020;
  --mf-z-index-fixed: 1030;
  --mf-z-index-modal-backdrop: 1040;
  --mf-z-index-modal: 1050;
  --mf-z-index-popover: 1060;
  --mf-z-index-tooltip: 1070;
  --mf-z-index-toast: 1080;
}

/* 暗色主题 */
@media (prefers-color-scheme: dark) {
  :root {
    --mf-text-primary: #ffffffd9;
    --mf-text-secondary: #ffffff73;
    --mf-text-tertiary: #ffffff40;
    --mf-text-quaternary: #ffffff26;
    
    --mf-bg-primary: #000000;
    --mf-bg-secondary: #141414;
    --mf-bg-tertiary: #1f1f1f;
    
    --mf-border-color: #424242;
    --mf-border-color-split: #303030;
  }
}

/* 框架基础样式 */
.mobile-framework {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif;
  font-size: var(--mf-font-size-md);
  line-height: var(--mf-line-height-md);
  color: var(--mf-text-primary);
  background-color: var(--mf-bg-primary);
}

/* 工具类 */
.mf-text-primary { color: var(--mf-text-primary); }
.mf-text-secondary { color: var(--mf-text-secondary); }
.mf-text-tertiary { color: var(--mf-text-tertiary); }

.mf-bg-primary { background-color: var(--mf-bg-primary); }
.mf-bg-secondary { background-color: var(--mf-bg-secondary); }
.mf-bg-tertiary { background-color: var(--mf-bg-tertiary); }

.mf-border { border: 1px solid var(--mf-border-color); }
.mf-border-split { border: 1px solid var(--mf-border-color-split); }

.mf-shadow-sm { box-shadow: var(--mf-shadow-sm); }
.mf-shadow-md { box-shadow: var(--mf-shadow-md); }
.mf-shadow-lg { box-shadow: var(--mf-shadow-lg); }

.mf-rounded-sm { border-radius: var(--mf-border-radius-sm); }
.mf-rounded-md { border-radius: var(--mf-border-radius-md); }
.mf-rounded-lg { border-radius: var(--mf-border-radius-lg); }

/* 响应式断点 */
@media (max-width: 576px) {
  .mobile-framework {
    font-size: var(--mf-font-size-sm);
  }
}

/* Ant Design Mobile 样式覆盖 */
.antd-mobile-icon {
  display: inline;
}

/* 确保组件样式优先级 */
.mobile-framework .adm-button {
  font-family: inherit;
}

.mobile-framework .adm-list-item {
  font-family: inherit;
}
