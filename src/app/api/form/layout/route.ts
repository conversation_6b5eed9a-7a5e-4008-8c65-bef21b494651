import { NextResponse } from 'next/server';

// 表单页面布局的mock数据
const mockLayoutData = {
  "layout": {
      "layout": [
          {
              "id": "1e9bbjh534e042a4b2f511fe124i195e",
              "bizId": "98b155faad834688bc503c66287788ce",
              "code": "WRAPPER_FLEX_CONTAINER-10IB21HJ362G4FD6BJJF105ADJ2FG3BF",
              "name": "flex-2ebh286i10hd4d43b62831dh8jcdb9j8",
              "type": "FLEX_CONTAINER",
              "kind": "LAYOUT_CONTAINER",
              "parentId": "-1",
              "relateId": "1e9bbjh534e042a4b2f511fe124i195e",
              "sortNo": 1,
              "bizType": "LCP_FORM_DESIGN_CHILDREN",
              "isEnabled": "1",
              "isDeleted": "0",
              "bizPapi": null,
              "bizPtype": null,
              "config": {
                  "rulesConfig": [],
                  "uiConfig": {
                      "extra": {
                          "layout": "1"
                      },
                      "attr": {
                          "style": {},
                          "span": 24
                      }
                  },
                  "baseConfig": {
                      "apiName": "WRAPPER_FLEX_CONTAINER-10ib21hj362g4fd6bjjf105adj2fg3bf",
                      "isWrapper": "1",
                      "isEditable": "1",
                      "name": "字段布局",
                      "isVisible": "1",
                      "attrType": "FLEX_CONTAINER"
                  }
              },
              "children": [
                  {
                      "id": "2j1h65473g6e4160b82463i2haj0g35a",
                      "bizId": "98b155faad834688bc503c66287788ce",
                      "code": "TAB_304CF387976878E0",
                      "name": "tab-3i6gc6aj2hdi4fe2bc401i153bh4846j",
                      "type": "TAB",
                      "kind": "FORM_CONTAINER",
                      "parentId": "1e9bbjh534e042a4b2f511fe124i195e",
                      "relateId": "2j1h65473g6e4160b82463i2haj0g35a",
                      "sortNo": 1,
                      "bizType": "LCP_FORM_DESIGN_CHILDREN",
                      "isEnabled": "1",
                      "isDeleted": "0",
                      "bizPapi": null,
                      "bizPtype": null,
                      "config": {
                          "rulesConfig": [],
                          "uiConfig": {
                              "attr": {
                                  "col": 24,
                                  "className": "lcp-margin-bottom"
                              }
                          },
                          "baseConfig": {
                              "apiName": "TAB_304CF387976878E0",
                              "isEditable": {
                                default: '1'
                              },
                              "name": "tab容器",
                              "isCrtVisible": "1",
                              "isVisible": "1",
                              "forceRender": "1",
                              "attrType": "TAB"
                          }
                      },
                      "children": [
                          {
                              "id": "288h5993200b4i4bbj214ih58325g602",
                              "bizId": "98b155faad834688bc503c66287788ce",
                              "code": "TABPANE_6F1D0344CE47E6F6",
                              "name": "基础表单",
                              "type": "CUSTOM_LAYOUT",
                              "kind": "TAG",
                              "parentId": "2j1h65473g6e4160b82463i2haj0g35a",
                              "relateId": "288h5993200b4i4bbj214ih58325g602",
                              "sortNo": 1,
                              "bizType": "LCP_FORM_DESIGN_CHILDREN",
                              "isEnabled": "1",
                              "isDeleted": "0",
                              "bizPapi": null,
                              "bizPtype": null,
                              "config": {
                                  "rulesConfig": [],
                                  "uiConfig": {
                                      "attr": {}
                                  },
                                  "baseConfig": {
                                      "apiName": "TABPANE_6F1D0344CE47E6F6",
                                      "isEditable": "1",
                                      "name": "基础表单",
                                      "tagType": "OBJ",
                                      "isCrtVisible": "1",
                                      "isVisible": "1",
                                      "attrType": "OBJ"
                                  }
                              },
                              "children": [
                                  {
                                      "id": "2c2ahc8c2j9b4gc9bfg828f26g1hb4f4",
                                      "bizId": "98b155faad834688bc503c66287788ce",
                                      "code": "FLEX_61E17D7E6156FB5C",
                                      "name": "flex-2h21baj915434078bg735ih3g911hf9g",
                                      "type": "FLEX_CONTAINER",
                                      "kind": "LAYOUT_CONTAINER",
                                      "parentId": "288h5993200b4i4bbj214ih58325g602",
                                      "relateId": "2c2ahc8c2j9b4gc9bfg828f26g1hb4f4",
                                      "sortNo": 1,
                                      "bizType": "LCP_FORM_DESIGN_CHILDREN",
                                      "isEnabled": "1",
                                      "isDeleted": "0",
                                      "bizPapi": null,
                                      "bizPtype": null,
                                      "config": {
                                          "rulesConfig": [],
                                          "uiConfig": {
                                              "extra": {
                                                  "layout": "1"
                                              },
                                              "attr": {
                                                  "style": {},
                                                  "span": 24
                                              }
                                          },
                                          "baseConfig": {
                                              "apiName": "FLEX_61E17D7E6156FB5C",
                                              "isWrapper": "0",
                                              "isEditable": "1",
                                              "name": "字段布局",
                                              "isVisible": "1",
                                              "attrType": "FLEX_CONTAINER"
                                          }
                                      },
                                      "children": [
                                          {
                                              "id": "21gf5d8d3dgi4fbdb27h2hf5b793962a",
                                              "bizId": "98b155faad834688bc503c66287788ce",
                                              "code": "TEXT_4E2444398774DB75",
                                              "name": "单行文本",
                                              "type": "CUSTOM_FIELD",
                                              "kind": "FORM_BASE",
                                              "parentId": "2c2ahc8c2j9b4gc9bfg828f26g1hb4f4",
                                              "relateId": "b7a71a3e59e64331bb1732d01b8726d7",
                                              "sortNo": 1,
                                              "bizType": "LCP_FORM_DESIGN_CHILDREN",
                                              "isEnabled": "1",
                                              "isDeleted": "0",
                                              "bizPapi": null,
                                              "bizPtype": null,
                                              "config": {
                                                  "rulesConfig": [
                                                      {
                                                          "isEnabled": "0",
                                                          "type": "required",
                                                          "message": "",
                                                          "value": "1"
                                                      },
                                                      {
                                                          "isEnabled": "1",
                                                          "type": "maxLength",
                                                          "message": "",
                                                          "value": 256
                                                      }
                                                  ],
                                                  "uiConfig": {
                                                      "attr": {
                                                          "labelAlign": "right",
                                                          "wrapperCol": {
                                                              "span": 20
                                                          },
                                                          "labelCol": {
                                                              "span": 4
                                                          }
                                                      }
                                                  },
                                                  "baseConfig": {
                                                      "commitType": "always",
                                                      "isChangeOn": "0",
                                                      "apiName": "TEXT_4E2444398774DB75",
                                                      "resource": "tz-render",
                                                      "formCode": "BASIC_INFO_GROUP",
                                                      "exportKey": "LcpBaseInputConfigs",
                                                      "mouldName": "基础信息",
                                                      "hyphen": "",
                                                      "isCrtVisible": "1",
                                                      "outputType": "string",
                                                      "isVisible": "1",
                                                      "attrType": "TEXT",
                                                      "importType": "local",
                                                      "mouldId": "BASIC_INFO_GROUP",
                                                      "isVersionOn": "0",
                                                      "isEditable": "1",
                                                      "isEnabled": "1",
                                                      "name": "单行文本",
                                                      "isCopiable": "1"
                                                  }
                                              },
                                              "children": [],
                                              "buttons": null
                                          },
                                          {
                                              "id": "4bdaa0e12dh240c0b46f345hef5j9d1j",
                                              "bizId": "98b155faad834688bc503c66287788ce",
                                              "code": "INT_603BC537EF74D726",
                                              "name": "数字",
                                              "type": "CUSTOM_FIELD",
                                              "kind": "FORM_BASE",
                                              "parentId": "2c2ahc8c2j9b4gc9bfg828f26g1hb4f4",
                                              "relateId": "e74ed67989b14102b295e727cb1edb17",
                                              "sortNo": 2,
                                              "bizType": "LCP_FORM_DESIGN_CHILDREN",
                                              "isEnabled": "1",
                                              "isDeleted": "0",
                                              "bizPapi": null,
                                              "bizPtype": null,
                                              "config": {
                                                  "rulesConfig": [
                                                      {
                                                          "isEnabled": "0",
                                                          "type": "required",
                                                          "message": "",
                                                          "value": "1"
                                                      },
                                                      {
                                                          "isEnabled": "1",
                                                          "type": "maxLength",
                                                          "message": "",
                                                          "value": 20
                                                      },
                                                      {
                                                          "isEnabled": "1",
                                                          "type": "accuracy",
                                                          "message": "",
                                                          "value": 0
                                                      }
                                                  ],
                                                  "uiConfig": {
                                                      "attr": {
                                                          "labelAlign": "right",
                                                          "wrapperCol": {
                                                              "span": 20
                                                          },
                                                          "labelCol": {
                                                              "span": 4
                                                          }
                                                      }
                                                  },
                                                  "baseConfig": {
                                                      "commitType": "always",
                                                      "isChangeOn": "0",
                                                      "apiName": "INT_603BC537EF74D726",
                                                      "resource": "tz-render",
                                                      "formCode": "BASIC_INFO_GROUP",
                                                      "exportKey": "LcpBaseInputNumberConfigs",
                                                      "mouldName": "基础信息",
                                                      "hyphen": "",
                                                      "isCrtVisible": "1",
                                                      "outputType": "string",
                                                      "isVisible": "1",
                                                      "attrType": "INT",
                                                      "importType": "local",
                                                      "mouldId": "BASIC_INFO_GROUP",
                                                      "isVersionOn": "0",
                                                      "isEditable": "1",
                                                      "isEnabled": "1",
                                                      "name": "数字",
                                                      "isCopiable": "1"
                                                  }
                                              },
                                              "children": [],
                                              "buttons": null
                                          },
                                          {
                                              "id": "2c8fb7bid1ee4fjhb3j73chah9igci55",
                                              "bizId": "98b155faad834688bc503c66287788ce",
                                              "code": "TEXTAREA_6EDE4EE71A930CE6",
                                              "name": "多行文本",
                                              "type": "CUSTOM_FIELD",
                                              "kind": "FORM_BASE",
                                              "parentId": "2c2ahc8c2j9b4gc9bfg828f26g1hb4f4",
                                              "relateId": "443240e6395143e89e0101e99973f4cc",
                                              "sortNo": 3,
                                              "bizType": "LCP_FORM_DESIGN_CHILDREN",
                                              "isEnabled": "1",
                                              "isDeleted": "0",
                                              "bizPapi": null,
                                              "bizPtype": null,
                                              "config": {
                                                  "rulesConfig": [
                                                      {
                                                          "isEnabled": "0",
                                                          "type": "required",
                                                          "message": "",
                                                          "value": "1"
                                                      },
                                                      {
                                                          "isEnabled": "1",
                                                          "type": "maxLength",
                                                          "message": "",
                                                          "value": 1024
                                                      }
                                                  ],
                                                  "uiConfig": {
                                                      "attr": {
                                                          "labelAlign": "right",
                                                          "wrapperCol": {
                                                              "span": 20
                                                          },
                                                          "labelCol": {
                                                              "span": 4
                                                          }
                                                      }
                                                  },
                                                  "baseConfig": {
                                                      "commitType": "always",
                                                      "isChangeOn": "0",
                                                      "apiName": "TEXTAREA_6EDE4EE71A930CE6",
                                                      "resource": "tz-render",
                                                      "formCode": "BASIC_INFO_GROUP",
                                                      "exportKey": "LcpBaseTextAreaConfigs",
                                                      "mouldName": "基础信息",
                                                      "hyphen": "",
                                                      "isCrtVisible": "1",
                                                      "outputType": "string",
                                                      "isVisible": "1",
                                                      "attrType": "TEXTAREA",
                                                      "importType": "local",
                                                      "mouldId": "BASIC_INFO_GROUP",
                                                      "isVersionOn": "0",
                                                      "isEditable": "1",
                                                      "isEnabled": "1",
                                                      "name": "多行文本",
                                                      "isCopiable": "1"
                                                  }
                                              },
                                              "children": [],
                                              "buttons": null
                                          },
                                          {
                                              "id": "4b0fi7bf2dhd4h1hb0301dej16g26890",
                                              "bizId": "98b155faad834688bc503c66287788ce",
                                              "code": "SELECT_57AB57F5B0860F3B",
                                              "name": "下拉单选",
                                              "type": "CUSTOM_FIELD",
                                              "kind": "FORM_BASE",
                                              "parentId": "2c2ahc8c2j9b4gc9bfg828f26g1hb4f4",
                                              "relateId": "08ce37ca9b854d629b79f9185b986422",
                                              "sortNo": 4,
                                              "bizType": "LCP_FORM_DESIGN_CHILDREN",
                                              "isEnabled": "1",
                                              "isDeleted": "0",
                                              "bizPapi": null,
                                              "bizPtype": null,
                                              "config": {
                                                  "rulesConfig": [
                                                      {
                                                          "isEnabled": "0",
                                                          "type": "required",
                                                          "message": "",
                                                          "value": "1"
                                                      },
                                                      {
                                                          "isEnabled": "1",
                                                          "type": "maxLength",
                                                          "message": "",
                                                          "value": 128
                                                      }
                                                  ],
                                                  "uiConfig": {
                                                      "attr": {
                                                          "labelAlign": "right",
                                                          "wrapperCol": {
                                                              "span": 20
                                                          },
                                                          "labelCol": {
                                                              "span": 4
                                                          }
                                                      }
                                                  },
                                                  "baseConfig": {
                                                      "commitType": "always",
                                                      "isChangeOn": "0",
                                                      "apiName": "SELECT_57AB57F5B0860F3B",
                                                      "resource": "tz-render",
                                                      "formCode": "BASIC_INFO_GROUP",
                                                      "exportKey": "LcpBaseSelectConfigs",
                                                      "mouldName": "基础信息",
                                                      "hyphen": "",
                                                      "isCrtVisible": "1",
                                                      "outputType": "string",
                                                      "isVisible": "1",
                                                      "dictId": "PCM_COMMON_ENABLE",
                                                      "attrType": "SELECT",
                                                      "importType": "local",
                                                      "mouldId": "BASIC_INFO_GROUP",
                                                      "isVersionOn": "0",
                                                      "isEditable": "1",
                                                      "isEnabled": "1",
                                                      "name": "下拉单选",
                                                      "isCopiable": "1"
                                                  }
                                              },
                                              "children": [],
                                              "buttons": null
                                          },
                                          {
                                              "id": "33cicjgg3ic9459gb6ej52he9gg83ebc",
                                              "bizId": "98b155faad834688bc503c66287788ce",
                                              "code": "MULTI_SELECT_55AC3785B77188BB",
                                              "name": "下拉多选",
                                              "type": "CUSTOM_FIELD",
                                              "kind": "FORM_BASE",
                                              "parentId": "2c2ahc8c2j9b4gc9bfg828f26g1hb4f4",
                                              "relateId": "55b4953caf6043d8b705f1a49227931a",
                                              "sortNo": 5,
                                              "bizType": "LCP_FORM_DESIGN_CHILDREN",
                                              "isEnabled": "1",
                                              "isDeleted": "0",
                                              "bizPapi": null,
                                              "bizPtype": null,
                                              "config": {
                                                  "rulesConfig": [
                                                      {
                                                          "isEnabled": "0",
                                                          "type": "required",
                                                          "message": "",
                                                          "value": "1"
                                                      },
                                                      {
                                                          "isEnabled": "1",
                                                          "type": "maxLength",
                                                          "message": "",
                                                          "value": 1024
                                                      }
                                                  ],
                                                  "uiConfig": {
                                                      "attr": {
                                                          "labelAlign": "right",
                                                          "wrapperCol": {
                                                              "span": 20
                                                          },
                                                          "labelCol": {
                                                              "span": 4
                                                          }
                                                      }
                                                  },
                                                  "baseConfig": {
                                                      "commitType": "always",
                                                      "isChangeOn": "0",
                                                      "apiName": "MULTI_SELECT_55AC3785B77188BB",
                                                      "resource": "tz-render",
                                                      "formCode": "BASIC_INFO_GROUP",
                                                      "exportKey": "LcpBaseMultiSelectConfigs",
                                                      "mouldName": "基础信息",
                                                      "hyphen": "",
                                                      "isCrtVisible": "1",
                                                      "outputType": "string",
                                                      "isVisible": "1",
                                                      "dictId": "CPLM_OBJ_ORG_UNIT",
                                                      "attrType": "MULTI_SELECT",
                                                      "importType": "local",
                                                      "mouldId": "BASIC_INFO_GROUP",
                                                      "isVersionOn": "0",
                                                      "isEditable": "1",
                                                      "isEnabled": "1",
                                                      "name": "下拉多选",
                                                      "isCopiable": "1"
                                                  }
                                              },
                                              "children": [],
                                              "buttons": null
                                          },
                                          {
                                              "id": "ebi09c16286848edbhe530h70ei778d2",
                                              "bizId": "98b155faad834688bc503c66287788ce",
                                              "code": "RADIO_24F882D90C96A301",
                                              "name": "单选框",
                                              "type": "CUSTOM_FIELD",
                                              "kind": "FORM_BASE",
                                              "parentId": "2c2ahc8c2j9b4gc9bfg828f26g1hb4f4",
                                              "relateId": "14cff52386094f388c560eca04426520",
                                              "sortNo": 6,
                                              "bizType": "LCP_FORM_DESIGN_CHILDREN",
                                              "isEnabled": "1",
                                              "isDeleted": "0",
                                              "bizPapi": null,
                                              "bizPtype": null,
                                              "config": {
                                                  "rulesConfig": [
                                                      {
                                                          "isEnabled": "0",
                                                          "type": "required",
                                                          "message": "",
                                                          "value": "1"
                                                      },
                                                      {
                                                          "isEnabled": "1",
                                                          "type": "maxLength",
                                                          "message": "",
                                                          "value": 128
                                                      }
                                                  ],
                                                  "uiConfig": {
                                                      "attr": {
                                                          "labelAlign": "right",
                                                          "wrapperCol": {
                                                              "span": 20
                                                          },
                                                          "labelCol": {
                                                              "span": 4
                                                          }
                                                      }
                                                  },
                                                  "baseConfig": {
                                                      "commitType": "always",
                                                      "isChangeOn": "0",
                                                      "apiName": "RADIO_24F882D90C96A301",
                                                      "resource": "tz-render",
                                                      "formCode": "BASIC_INFO_GROUP",
                                                      "exportKey": "LcpBaseRadioConfigs",
                                                      "mouldName": "基础信息",
                                                      "hyphen": "",
                                                      "isCrtVisible": "1",
                                                      "outputType": "string",
                                                      "isVisible": "1",
                                                      "dictId": "CPLM_PRODUCT_SELF_OR_OUTSIDE",
                                                      "attrType": "RADIO",
                                                      "importType": "local",
                                                      "mouldId": "BASIC_INFO_GROUP",
                                                      "isVersionOn": "0",
                                                      "isEditable": "1",
                                                      "isEnabled": "1",
                                                      "name": "单选框",
                                                      "isCopiable": "1"
                                                  }
                                              },
                                              "children": [],
                                              "buttons": null
                                          },
                                          {
                                              "id": "3ebhf9gb1cfe4de6bj17255ih2bbb0hg",
                                              "bizId": "98b155faad834688bc503c66287788ce",
                                              "code": "SEARCH_43664B9F17814F9C",
                                              "name": "单选搜索",
                                              "type": "CUSTOM_FIELD",
                                              "kind": "FORM_BASE",
                                              "parentId": "2c2ahc8c2j9b4gc9bfg828f26g1hb4f4",
                                              "relateId": "cd793c1d022d48a7b9951eb495a6820f",
                                              "sortNo": 7,
                                              "bizType": "LCP_FORM_DESIGN_CHILDREN",
                                              "isEnabled": "1",
                                              "isDeleted": "0",
                                              "bizPapi": null,
                                              "bizPtype": null,
                                              "config": {
                                                  "rulesConfig": [
                                                      {
                                                          "isEnabled": "0",
                                                          "type": "required",
                                                          "message": "",
                                                          "value": "1"
                                                      },
                                                      {
                                                          "isEnabled": "1",
                                                          "type": "maxLength",
                                                          "message": "",
                                                          "value": 128
                                                      }
                                                  ],
                                                  "uiConfig": {
                                                      "attr": {
                                                          "labelAlign": "right",
                                                          "wrapperCol": {
                                                              "span": 20
                                                          },
                                                          "labelCol": {
                                                              "span": 4
                                                          }
                                                      }
                                                  },
                                                  "baseConfig": {
                                                      "commitType": "always",
                                                      "isChangeOn": "0",
                                                      "apiName": "SEARCH_43664B9F17814F9C",
                                                      "resource": "tz-render",
                                                      "formCode": "BASIC_INFO_GROUP",
                                                      "exportKey": "LcpBaseSearchConfigs",
                                                      "mouldName": "基础信息",
                                                      "hyphen": "",
                                                      "isCrtVisible": "1",
                                                      "outputType": "string",
                                                      "isVisible": "1",
                                                      "dictId": "CPLM_PRODUCT_CPLM_PRODUCT_MODEL",
                                                      "attrType": "SEARCH",
                                                      "importType": "local",
                                                      "mouldId": "BASIC_INFO_GROUP",
                                                      "isVersionOn": "0",
                                                      "isEditable": "1",
                                                      "dictConfig": {
                                                          "type": "dict"
                                                      },
                                                      "isEnabled": "1",
                                                      "name": "单选搜索",
                                                      "isCopiable": "1"
                                                  }
                                              },
                                              "children": [],
                                              "buttons": null
                                          }
                                      ],
                                      "buttons": null
                                  }
                              ],
                              "buttons": null
                          }
                      ],
                      "buttons": null
                  }
              ],
              "buttons": null
          }
      ],
      "classId": "e527c3490bb0498f9da8efb468c7672a",
      "currentNodeCode": null,
      "globalConfig": {
          "config": {
              "styleConfig": {
                  "labelWidth": "122px"
              }
          }
      },
      "code": "LCP_QBTYB",
      "baseLayoutConfig": {
          "backTopConfig": {
              "baseConfig": {
                  "isEnabled": "0"
              }
          },
          "anchorConfig": {
              "uiConfig": {
                  "align": "right"
              },
              "baseConfig": {
                  "isEnabled": "0"
              }
          },
          "buttonGroup": {
              "buttons": [
                  {
                      "code": "BUTTON_530B827BF7E80CBB",
                      "kind": "BUTTON",
                      "name": "保存",
                      "id": "21a0gfhb3dee443eb4c82hgh840eba75",
                      "type": "BUTTON",
                      "config": {
                          "rulesConfig": [],
                          "uiConfig": {
                              "extra": {
                                  "showType": "button"
                              },
                              "attr": {
                                  "size": "middle",
                                  "type": "primary"
                              }
                          },
                          "baseConfig": {
                              "apiName": "BUTTON_530B827BF7E80CBB",
                              "isEditable": "1",
                              "buttonTemplateId": "36eea9e521e14641b85500ce38b6f452",
                              "name": "保存",
                              "isVisible": "1",
                              "event": {
                                  "runType": "local",
                                  "name": "CUSTOM",
                                  "id": "b5f85d9d67e24761bf7034bab9d1ebaf",
                                  "customEventName": "__commonSave"
                              },
                              "attrType": "BUTTON"
                          }
                      }
                  }
              ],
              "baseConfig": {
                  "buttonAlign": "right",
                  "isVisible": "1",
                  "position": "top"
              }
          },
          "flexContainer": {
              "styleConfig": {
                  "col": 24
              }
          }
      },
      "schemeName": "1",
      "schemeId": "98b155faad834688bc503c66287788ce",
      "classApiName": "LCP_QBTYB",
      "id": null,
      "ownerId": null
  },
  "data": null,
  "layoutAttrMap": {
      "BASIC_INFO_GROUP": [
          {
              "id": "b7a71a3e59e64331bb1732d01b8726d7",
              "tableName": null,
              "attributeGroupId": null,
              "attributeGroupApiName": null,
              "name": "单行文本",
              "apiName": "TEXT_4E2444398774DB75",
              "attrType": "TEXT",
              "dictId": null,
              "dictConfig": null,
              "defaultValue": null,
              "isVisible": "1",
              "isVirtual": null,
              "dateFormatter": null,
              "addType": null,
              "unitValueSet": null,
              "formCode": null,
              "formId": null
          },
          {
              "id": "443240e6395143e89e0101e99973f4cc",
              "tableName": null,
              "attributeGroupId": null,
              "attributeGroupApiName": null,
              "name": "多行文本",
              "apiName": "TEXTAREA_6EDE4EE71A930CE6",
              "attrType": "TEXTAREA",
              "dictId": null,
              "dictConfig": null,
              "defaultValue": null,
              "isVisible": "1",
              "isVirtual": null,
              "dateFormatter": null,
              "addType": null,
              "unitValueSet": null,
              "formCode": null,
              "formId": null
          },
          {
              "id": "55b4953caf6043d8b705f1a49227931a",
              "tableName": null,
              "attributeGroupId": null,
              "attributeGroupApiName": null,
              "name": "下拉多选",
              "apiName": "MULTI_SELECT_55AC3785B77188BB",
              "attrType": "MULTI_SELECT",
              "dictId": "CPLM_OBJ_ORG_UNIT",
              "dictConfig": null,
              "defaultValue": null,
              "isVisible": "1",
              "isVirtual": null,
              "dateFormatter": null,
              "addType": null,
              "unitValueSet": null,
              "formCode": null,
              "formId": null
          },
          {
              "id": "cd793c1d022d48a7b9951eb495a6820f",
              "tableName": null,
              "attributeGroupId": null,
              "attributeGroupApiName": null,
              "name": "单选搜索",
              "apiName": "SEARCH_43664B9F17814F9C",
              "attrType": "SEARCH",
              "dictId": "CPLM_PRODUCT_CPLM_PRODUCT_MODEL",
              "dictConfig": {
                  "type": "dict"
              },
              "defaultValue": null,
              "isVisible": "1",
              "isVirtual": null,
              "dateFormatter": null,
              "addType": null,
              "unitValueSet": null,
              "formCode": null,
              "formId": null
          },
          {
              "id": "08ce37ca9b854d629b79f9185b986422",
              "tableName": null,
              "attributeGroupId": null,
              "attributeGroupApiName": null,
              "name": "下拉单选",
              "apiName": "SELECT_57AB57F5B0860F3B",
              "attrType": "SELECT",
              "dictId": "PCM_COMMON_ENABLE",
              "dictConfig": null,
              "defaultValue": null,
              "isVisible": "1",
              "isVirtual": null,
              "dateFormatter": null,
              "addType": null,
              "unitValueSet": null,
              "formCode": null,
              "formId": null
          },
          {
              "id": "e74ed67989b14102b295e727cb1edb17",
              "tableName": null,
              "attributeGroupId": null,
              "attributeGroupApiName": null,
              "name": "数字",
              "apiName": "INT_603BC537EF74D726",
              "attrType": "INT",
              "dictId": null,
              "dictConfig": null,
              "defaultValue": null,
              "isVisible": "1",
              "isVirtual": null,
              "dateFormatter": null,
              "addType": null,
              "unitValueSet": null,
              "formCode": null,
              "formId": null
          },
          {
              "id": "14cff52386094f388c560eca04426520",
              "tableName": null,
              "attributeGroupId": null,
              "attributeGroupApiName": null,
              "name": "单选框",
              "apiName": "RADIO_24F882D90C96A301",
              "attrType": "RADIO",
              "dictId": "CPLM_PRODUCT_SELF_OR_OUTSIDE",
              "dictConfig": null,
              "defaultValue": null,
              "isVisible": "1",
              "isVirtual": null,
              "dateFormatter": null,
              "addType": null,
              "unitValueSet": null,
              "formCode": null,
              "formId": null
          }
      ]
  },
  "apiConfigMap": {
      "batchDownload": {
          "url": "https://csbtest-api.gz.cvte.cn/cfile/c518f53d-b405-4111-afe1-5c082b284971/v2/batch_download",
          "method": "post",
          "params": null,
          "headers": null
      },
      "preview": {
          "url": "https://csbtest-api.gz.cvte.cn/cfile/c518f53d-b405-4111-afe1-5c082b284971/v2/preview",
          "method": "get",
          "params": null,
          "headers": null
      },
      "orgFetch": {
          "url": "/apis/common/proxy/lcpCore/v1/app/org/relation_tree",
          "method": "get",
          "params": null,
          "headers": null
      },
      "download": {
          "url": "https://csbtest-api.gz.cvte.cn/cfile/c518f53d-b405-4111-afe1-5c082b284971/v2/download",
          "method": "get",
          "params": null,
          "headers": null
      },
      "objUpload": {
          "url": "/apis/common/proxy/lcpCore/obj-file/callback",
          "method": "post",
          "params": {
              "classId": "e527c3490bb0498f9da8efb468c7672a",
              "objId": "huangyunzhen",
              "objClassId": "e527c3490bb0498f9da8efb468c7672a"
          },
          "headers": null
      },
      "getInfo": {
          "url": "/apis/common/proxy/annex/c518f53d-b405-4111-afe1-5c082b284971/v2/files",
          "method": "post",
          "params": null,
          "headers": null
      },
      "upload": {
          "url": "/apis/common/proxy/annex/c518f53d-b405-4111-afe1-5c082b284971/v2/upload/batch",
          "method": "post",
          "params": {
              "catalogId": "huangyunzhen",
              "categoryId": "e527c3490bb0498f9da8efb468c7672a"
          },
          "headers": null
      },
      "userFetch": {
          "url": "/apis/common/proxy/lcpCore/v1/app/user/search",
          "method": "post",
          "params": null,
          "headers": null
      },
      "tagUrlPreFix": {
          "url": "/apis/common/proxy/lcpGw/hive-center/apis/v1",
          "method": "get",
          "params": null,
          "headers": null
      }
  },
  "relations": {},
  "contextDataMap": {
      "contextData": {},
      "user": null,
      "org": null
  }
};

/**
 * GET 请求处理函数
 * 返回表单页面的布局配置
 */
export async function GET() {
  // 这里可以添加参数处理、验证等逻辑

  // 模拟API延迟
  await new Promise(resolve => setTimeout(resolve, 500));

  // 返回mock数据
  return NextResponse.json({
    code: 0,
    message: '获取成功',
    data: mockLayoutData
  });
}