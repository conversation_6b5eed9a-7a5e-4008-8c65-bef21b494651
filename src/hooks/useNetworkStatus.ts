import { useState, useEffect } from 'react';

/**
 * 网络状态Hook
 * 监听网络连接状态变化
 */
const useNetworkStatus = () => {
  const [isOnline, setIsOnline] = useState(true);
  const [networkType, setNetworkType] = useState<string>('unknown');

  useEffect(() => {
    // 初始化网络状态
    setIsOnline(navigator.onLine);

    // 获取网络类型（如果支持）
    if ('connection' in navigator) {
      const connection = (navigator as any).connection;
      setNetworkType(connection?.effectiveType || 'unknown');
    }

    // 监听网络状态变化
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    // 监听网络类型变化
    const handleConnectionChange = () => {
      if ('connection' in navigator) {
        const connection = (navigator as any).connection;
        setNetworkType(connection?.effectiveType || 'unknown');
      }
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    if ('connection' in navigator) {
      const connection = (navigator as any).connection;
      connection?.addEventListener('change', handleConnectionChange);
    }

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
      
      if ('connection' in navigator) {
        const connection = (navigator as any).connection;
        connection?.removeEventListener('change', handleConnectionChange);
      }
    };
  }, []);

  return {
    isOnline,
    networkType,
  };
};

export default useNetworkStatus;
