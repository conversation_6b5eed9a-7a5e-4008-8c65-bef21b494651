/**
 * 自定义Hooks模块导出
 * 提供常用的React Hooks
 */

// 目前hooks目录为空，这里提供一些基础的hooks作为示例

/**
 * 网络状态Hook
 */
export { default as useNetworkStatus } from './useNetworkStatus';

/**
 * 本地存储Hook
 */
export { default as useLocalStorage } from './useLocalStorage';

/**
 * 防抖Hook
 */
export { default as useDebounce } from './useDebounce';

/**
 * 节流Hook
 */
export { default as useThrottle } from './useThrottle';

/**
 * 设备信息Hook
 */
export { default as useDeviceInfo } from './useDeviceInfo';
