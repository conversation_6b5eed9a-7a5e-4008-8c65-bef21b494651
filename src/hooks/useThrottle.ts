import { useState, useEffect, useRef } from 'react';

/**
 * 节流Hook
 * 在指定时间间隔内最多执行一次更新
 */
const useThrottle = <T>(value: T, interval: number): T => {
  const [throttledValue, setThrottledValue] = useState<T>(value);
  const lastExecuted = useRef<number>(Date.now());

  useEffect(() => {
    const now = Date.now();
    const timeSinceLastExecution = now - lastExecuted.current;

    if (timeSinceLastExecution >= interval) {
      // 如果距离上次执行已经超过间隔时间，立即更新
      setThrottledValue(value);
      lastExecuted.current = now;
    } else {
      // 否则设置定时器在剩余时间后更新
      const timer = setTimeout(() => {
        setThrottledValue(value);
        lastExecuted.current = Date.now();
      }, interval - timeSinceLastExecution);

      return () => clearTimeout(timer);
    }
  }, [value, interval]);

  return throttledValue;
};

export default useThrottle;
