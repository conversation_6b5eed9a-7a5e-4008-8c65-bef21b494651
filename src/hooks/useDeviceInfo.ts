import { useState, useEffect } from 'react';
import { getDeviceInfo } from '@/utils';

/**
 * 设备信息Hook
 * 获取设备类型、用户代理等信息
 */
const useDeviceInfo = () => {
  const [deviceInfo, setDeviceInfo] = useState(() => getDeviceInfo());

  useEffect(() => {
    // 监听窗口大小变化，重新检测设备类型
    const handleResize = () => {
      setDeviceInfo(getDeviceInfo());
    };

    window.addEventListener('resize', handleResize);
    
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  return deviceInfo;
};

export default useDeviceInfo;
