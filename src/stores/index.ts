import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { logger } from '@/lib/logger';

/**
 * 用户信息接口
 */
export interface UserInfo {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  roles: string[];
}

/**
 * 应用状态接口
 */
export interface AppState {
  // 用户相关状态
  user: UserInfo | null;
  isAuthenticated: boolean;

  // UI状态
  loading: boolean;
  theme: 'light' | 'dark';
  sidebarCollapsed: boolean;

  // 网络状态
  isOnline: boolean;

  // 操作方法
  setUser: (user: UserInfo | null) => void;
  setLoading: (loading: boolean) => void;
  setTheme: (theme: 'light' | 'dark') => void;
  toggleSidebar: () => void;
  setOnlineStatus: (isOnline: boolean) => void;
  logout: () => void;
  reset: () => void;
}

/**
 * 初始状态
 */
const initialState = {
  user: null,
  isAuthenticated: false,
  loading: false,
  theme: 'light' as const,
  sidebarCollapsed: false,
  isOnline: true,
};

/**
 * 创建应用状态store
 */
export const useAppStore = create<AppState>()(
  devtools(
    persist(
      (set, get) => ({
        ...initialState,

        setUser: (user) => {
          logger.info('User state updated:', { userId: user?.id });
          set(
            () => ({
              user,
              isAuthenticated: !!user,
            }),
            false,
            'setUser'
          );
        },

        setLoading: (loading) => {
          set({ loading }, false, 'setLoading');
        },

        setTheme: (theme) => {
          logger.info('Theme changed:', { theme });
          set({ theme }, false, 'setTheme');
        },

        toggleSidebar: () => {
          set(
            (state) => ({ sidebarCollapsed: !state.sidebarCollapsed }),
            false,
            'toggleSidebar'
          );
        },

        setOnlineStatus: (isOnline) => {
          if (get().isOnline !== isOnline) {
            logger.info('Network status changed:', { isOnline });
            set({ isOnline }, false, 'setOnlineStatus');
          }
        },

        logout: () => {
          logger.info('User logged out');
          set(
            {
              user: null,
              isAuthenticated: false,
            },
            false,
            'logout'
          );

          // 清除本地存储的认证信息
          if (typeof window !== 'undefined') {
            localStorage.removeItem('auth_token');
          }
        },

        reset: () => {
          logger.info('App state reset');
          set(initialState, false, 'reset');
        },
      }),
      {
        name: 'app-store',
        partialize: (state) => ({
          user: state.user,
          isAuthenticated: state.isAuthenticated,
          theme: state.theme,
          sidebarCollapsed: state.sidebarCollapsed,
        }),
      }
    ),
    {
      name: 'app-store',
    }
  )
);

/**
 * 选择器hooks - 用于性能优化
 */
export const useUser = () => useAppStore((state) => state.user);
export const useIsAuthenticated = () => useAppStore((state) => state.isAuthenticated);
export const useLoading = () => useAppStore((state) => state.loading);
export const useTheme = () => useAppStore((state) => state.theme);
export const useSidebarCollapsed = () => useAppStore((state) => state.sidebarCollapsed);
export const useIsOnline = () => useAppStore((state) => state.isOnline);

/**
 * 操作方法hooks
 */
export const useAppActions = () => {
  const store = useAppStore();
  return {
    setUser: store.setUser,
    setLoading: store.setLoading,
    setTheme: store.setTheme,
    toggleSidebar: store.toggleSidebar,
    setOnlineStatus: store.setOnlineStatus,
    logout: store.logout,
    reset: store.reset,
  };
};
