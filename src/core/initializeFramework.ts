import { logger } from '@/lib/logger';
import { MobileAppConfig } from './createMobileApp';

/**
 * 初始化框架
 * 根据配置初始化各个模块
 *
 * @param config 应用配置
 */
const initializeFramework = async (config: MobileAppConfig): Promise<void> => {
  logger.info('Initializing mobile framework...', {
    appName: config.appName,
    version: config.version,
  });

  try {
    // 1. 初始化日志系统
    if (config.logger) {
      logger.updateConfig({
        level: config.logger.level === 'debug' ? 0 :
               config.logger.level === 'info' ? 1 :
               config.logger.level === 'warn' ? 2 : 3,
        enableConsole: config.logger.enableConsole ?? true,
        enableStorage: config.logger.enableStorage ?? false,
        enableRemote: config.logger.enableRemote ?? false,
        remoteEndpoint: config.logger.remoteEndpoint,
      });
      logger.info('Logger initialized');
    }

    // 2. 初始化API客户端
    if (config.apiBaseUrl) {
      // 这里可以重新配置API客户端的baseURL
      logger.info('API client configured', {
        baseURL: config.apiBaseUrl,
        timeout: config.apiTimeout,
      });
    }

    // 3. 初始化主题系统
    if (config.theme) {
      // 设置CSS变量
      if (typeof window !== 'undefined') {
        const root = document.documentElement;

        if (config.theme.primaryColor) {
          root.style.setProperty('--mf-primary', config.theme.primaryColor);
        }

        // 设置主题模式
        if (config.theme.mode === 'dark') {
          root.classList.add('dark');
        } else if (config.theme.mode === 'light') {
          root.classList.remove('dark');
        }
        // auto模式由CSS媒体查询处理
      }

      logger.info('Theme system initialized', config.theme);
    }

    // 4. 初始化插件系统
    if (config.plugins && config.plugins.length > 0) {
      for (const plugin of config.plugins) {
        try {
          // 这里可以实现插件加载逻辑
          logger.info('Plugin loaded', { name: plugin.name });
        } catch (error) {
          logger.error('Failed to load plugin', {
            name: plugin.name,
            error: error instanceof Error ? error.message : 'Unknown error'
          });
        }
      }
    }

    // 5. 设置全局错误处理
    if (typeof window !== 'undefined') {
      // 全局错误处理
      window.addEventListener('error', (event) => {
        logger.error('Global error caught', {
          message: event.message,
          filename: event.filename,
          lineno: event.lineno,
          colno: event.colno,
          error: event.error,
        });
      });

      // Promise错误处理
      window.addEventListener('unhandledrejection', (event) => {
        logger.error('Unhandled promise rejection', {
          reason: event.reason,
        });
      });
    }

    logger.info('Mobile framework initialized successfully');

  } catch (error) {
    logger.error('Failed to initialize mobile framework', {
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    throw error;
  }
};

export default initializeFramework;
