import { logger } from '@/lib/logger';

/**
 * 移动应用配置接口
 */
export interface MobileAppConfig {
  // 应用基础信息
  appName: string;
  version: string;

  // Next.js配置
  appDir?: string;
  basePath?: string;

  // API配置
  apiBaseUrl?: string;
  apiTimeout?: number;

  // 主题配置
  theme?: {
    primaryColor?: string;
    mode?: 'light' | 'dark' | 'auto';
  };

  // 日志配置
  logger?: {
    level?: 'debug' | 'info' | 'warn' | 'error';
    enableConsole?: boolean;
    enableStorage?: boolean;
    enableRemote?: boolean;
    remoteEndpoint?: string;
  };

  // 缓存配置
  cache?: {
    defaultTTL?: number;
    maxSize?: number;
    enablePersistence?: boolean;
  };

  // 插件配置
  plugins?: Array<{
    name: string;
    config?: Record<string, any>;
  }>;

  // 自定义配置
  custom?: Record<string, any>;
}

/**
 * 移动应用实例接口
 */
export interface MobileAppInstance {
  config: MobileAppConfig;
  getConfig: () => MobileAppConfig;
  updateConfig: (config: Partial<MobileAppConfig>) => void;
  getAppDir: () => string;
  getApiBaseUrl: () => string;
  destroy: () => void;
}

/**
 * 创建移动应用实例
 *
 * @param config 应用配置
 * @returns 应用实例
 */
const createMobileApp = (config: MobileAppConfig): MobileAppInstance => {
  // 合并默认配置
  const defaultConfig: Partial<MobileAppConfig> = {
    version: '1.0.0',
    appDir: './src/app',
    basePath: '',
    apiBaseUrl: '/api',
    apiTimeout: 10000,
    theme: {
      mode: 'auto',
    },
    logger: {
      level: 'info',
      enableConsole: true,
      enableStorage: false,
      enableRemote: false,
    },
    cache: {
      defaultTTL: 5 * 60 * 1000, // 5分钟
      maxSize: 100,
      enablePersistence: true,
    },
    plugins: [],
    custom: {},
  };

  let appConfig = { ...defaultConfig, ...config } as MobileAppConfig;

  // 记录应用创建日志
  logger.info('Mobile app created', {
    appName: appConfig.appName,
    version: appConfig.version,
    appDir: appConfig.appDir,
  });

  // 创建应用实例
  const appInstance: MobileAppInstance = {
    config: appConfig,

    /**
     * 获取完整配置
     */
    getConfig() {
      return { ...appConfig };
    },

    /**
     * 更新配置
     */
    updateConfig(newConfig: Partial<MobileAppConfig>) {
      appConfig = { ...appConfig, ...newConfig };
      logger.info('Mobile app config updated', newConfig);
    },

    /**
     * 获取应用目录
     */
    getAppDir() {
      return appConfig.appDir || './src/app';
    },

    /**
     * 获取API基础URL
     */
    getApiBaseUrl() {
      return appConfig.apiBaseUrl || '/api';
    },

    /**
     * 销毁应用实例
     */
    destroy() {
      logger.info('Mobile app destroyed', {
        appName: appConfig.appName,
      });
    },
  };

  return appInstance;
};

export default createMobileApp;
