/**
 * 插件系统模块导出
 * 提供插件注册、加载、生命周期管理等功能
 */

/**
 * 插件接口
 */
export interface Plugin {
  name: string;
  version: string;
  description?: string;
  dependencies?: string[];

  // 插件生命周期方法
  install?: (config?: any) => Promise<void> | void;
  uninstall?: () => Promise<void> | void;
  activate?: () => Promise<void> | void;
  deactivate?: () => Promise<void> | void;

  // 插件配置
  config?: Record<string, any>;
}

/**
 * 插件管理器
 */
export class PluginManager {
  private plugins = new Map<string, Plugin>();
  private activePlugins = new Set<string>();

  /**
   * 注册插件
   */
  register(plugin: Plugin): void {
    if (this.plugins.has(plugin.name)) {
      throw new Error(`Plugin ${plugin.name} is already registered`);
    }

    this.plugins.set(plugin.name, plugin);
  }

  /**
   * 卸载插件
   */
  unregister(name: string): void {
    if (this.activePlugins.has(name)) {
      this.deactivate(name);
    }

    this.plugins.delete(name);
  }

  /**
   * 激活插件
   */
  async activate(name: string): Promise<void> {
    const plugin = this.plugins.get(name);
    if (!plugin) {
      throw new Error(`Plugin ${name} is not registered`);
    }

    if (this.activePlugins.has(name)) {
      return; // 已经激活
    }

    // 检查依赖
    if (plugin.dependencies) {
      for (const dep of plugin.dependencies) {
        if (!this.activePlugins.has(dep)) {
          throw new Error(`Plugin ${name} depends on ${dep}, but it's not active`);
        }
      }
    }

    // 安装插件
    if (plugin.install) {
      await plugin.install(plugin.config);
    }

    // 激活插件
    if (plugin.activate) {
      await plugin.activate();
    }

    this.activePlugins.add(name);
  }

  /**
   * 停用插件
   */
  async deactivate(name: string): Promise<void> {
    const plugin = this.plugins.get(name);
    if (!plugin) {
      throw new Error(`Plugin ${name} is not registered`);
    }

    if (!this.activePlugins.has(name)) {
      return; // 已经停用
    }

    // 停用插件
    if (plugin.deactivate) {
      await plugin.deactivate();
    }

    // 卸载插件
    if (plugin.uninstall) {
      await plugin.uninstall();
    }

    this.activePlugins.delete(name);
  }

  /**
   * 获取所有插件
   */
  getPlugins(): Plugin[] {
    return Array.from(this.plugins.values());
  }

  /**
   * 获取活跃插件
   */
  getActivePlugins(): Plugin[] {
    return Array.from(this.activePlugins)
      .map(name => this.plugins.get(name))
      .filter(Boolean) as Plugin[];
  }

  /**
   * 检查插件是否激活
   */
  isActive(name: string): boolean {
    return this.activePlugins.has(name);
  }
}

// 全局插件管理器实例
export const pluginManager = new PluginManager();

/**
 * 插件装饰器
 */
export function createPlugin(config: Omit<Plugin, 'install' | 'uninstall' | 'activate' | 'deactivate'>) {
  return function <T extends new (...args: any[]) => any>(constructor: T) {
    return class extends constructor implements Plugin {
      name = config.name;
      version = config.version;
      description = config.description;
      dependencies = config.dependencies;
      config = config.config;

      async install(_pluginConfig?: any): Promise<void> {
        // 子类可以重写此方法
      }

      async uninstall(): Promise<void> {
        // 子类可以重写此方法
      }

      async activate(): Promise<void> {
        // 子类可以重写此方法
      }

      async deactivate(): Promise<void> {
        // 子类可以重写此方法
      }
    };
  };
}
