/**
 * 日志级别枚举
 */
export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
}

/**
 * 日志条目接口
 */
export interface LogEntry {
  level: LogLevel;
  message: string;
  data?: any;
  timestamp: Date;
  source?: string;
}

/**
 * 日志配置接口
 */
export interface LoggerConfig {
  level: LogLevel;
  enableConsole: boolean;
  enableStorage: boolean;
  maxStorageEntries: number;
  enableRemote: boolean;
  remoteEndpoint?: string;
}

/**
 * 日志记录器类
 * 提供统一的日志记录功能，支持控制台输出、本地存储和远程上报
 */
class Logger {
  private config: LoggerConfig;
  private storageKey = 'app_logs';

  constructor(config?: Partial<LoggerConfig>) {
    this.config = {
      level: LogLevel.INFO,
      enableConsole: true,
      enableStorage: false,
      maxStorageEntries: 1000,
      enableRemote: false,
      ...config,
    };
  }

  /**
   * 记录调试日志
   */
  debug(message: string, data?: any, source?: string) {
    this.log(LogLevel.DEBUG, message, data, source);
  }

  /**
   * 记录信息日志
   */
  info(message: string, data?: any, source?: string) {
    this.log(LogLevel.INFO, message, data, source);
  }

  /**
   * 记录警告日志
   */
  warn(message: string, data?: any, source?: string) {
    this.log(LogLevel.WARN, message, data, source);
  }

  /**
   * 记录错误日志
   */
  error(message: string, data?: any, source?: string) {
    this.log(LogLevel.ERROR, message, data, source);
  }

  /**
   * 核心日志记录方法
   */
  private log(level: LogLevel, message: string, data?: any, source?: string) {
    // 检查日志级别
    if (level < this.config.level) {
      return;
    }

    const entry: LogEntry = {
      level,
      message,
      data,
      timestamp: new Date(),
      source,
    };

    // 控制台输出
    if (this.config.enableConsole) {
      this.logToConsole(entry);
    }

    // 本地存储
    if (this.config.enableStorage) {
      this.logToStorage(entry);
    }

    // 远程上报
    if (this.config.enableRemote && level >= LogLevel.WARN) {
      this.logToRemote(entry);
    }
  }

  /**
   * 输出到控制台
   */
  private logToConsole(entry: LogEntry) {
    const { level, message, data, timestamp, source } = entry;
    const timeStr = timestamp.toISOString();
    const sourceStr = source ? `[${source}]` : '';
    const fullMessage = `${timeStr} ${sourceStr} ${message}`;

    switch (level) {
      case LogLevel.DEBUG:
        console.debug(fullMessage, data);
        break;
      case LogLevel.INFO:
        console.info(fullMessage, data);
        break;
      case LogLevel.WARN:
        console.warn(fullMessage, data);
        break;
      case LogLevel.ERROR:
        console.error(fullMessage, data);
        break;
    }
  }

  /**
   * 存储到本地
   */
  private logToStorage(entry: LogEntry) {
    if (typeof window === 'undefined') return;

    try {
      const stored = localStorage.getItem(this.storageKey);
      const logs: LogEntry[] = stored ? JSON.parse(stored) : [];

      logs.push(entry);

      // 限制存储条目数量
      if (logs.length > this.config.maxStorageEntries) {
        logs.splice(0, logs.length - this.config.maxStorageEntries);
      }

      localStorage.setItem(this.storageKey, JSON.stringify(logs));
    } catch (error) {
      console.error('Failed to store log entry:', error);
    }
  }

  /**
   * 上报到远程服务器
   */
  private async logToRemote(entry: LogEntry) {
    if (!this.config.remoteEndpoint) return;

    try {
      await fetch(this.config.remoteEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(entry),
      });
    } catch (error) {
      console.error('Failed to send log to remote:', error);
    }
  }

  /**
   * 获取存储的日志
   */
  getStoredLogs(): LogEntry[] {
    if (typeof window === 'undefined') return [];

    try {
      const stored = localStorage.getItem(this.storageKey);
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      console.error('Failed to get stored logs:', error);
      return [];
    }
  }

  /**
   * 清除存储的日志
   */
  clearStoredLogs() {
    if (typeof window === 'undefined') return;

    try {
      localStorage.removeItem(this.storageKey);
    } catch (error) {
      console.error('Failed to clear stored logs:', error);
    }
  }

  /**
   * 更新配置
   */
  updateConfig(config: Partial<LoggerConfig>) {
    this.config = { ...this.config, ...config };
  }

  /**
   * 获取当前配置
   */
  getConfig(): LoggerConfig {
    return { ...this.config };
  }
}

// 创建默认日志记录器实例
export const logger = new Logger({
  level: process.env.NODE_ENV === 'development' ? LogLevel.DEBUG : LogLevel.INFO,
  enableConsole: true,
  enableStorage: process.env.NODE_ENV === 'development',
  enableRemote: process.env.NODE_ENV === 'production',
  remoteEndpoint: process.env.NEXT_PUBLIC_LOG_ENDPOINT,
});

// 导出类型和枚举
export { Logger };
