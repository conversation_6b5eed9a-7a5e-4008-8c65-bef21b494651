import { logger } from '@/lib/logger';

/**
 * 缓存条目接口
 */
export interface CacheEntry<T = any> {
  data: T;
  timestamp: number;
  ttl: number; // 生存时间（毫秒）
}

/**
 * 缓存配置接口
 */
export interface CacheConfig {
  defaultTTL: number; // 默认生存时间（毫秒）
  maxSize: number; // 最大缓存条目数
  enablePersistence: boolean; // 是否启用持久化
  storagePrefix: string; // 存储前缀
}

/**
 * 缓存管理器类
 * 提供内存缓存和持久化缓存功能，支持TTL、LRU淘汰策略
 */
class CacheManager {
  private memoryCache = new Map<string, CacheEntry>();
  private accessOrder = new Map<string, number>(); // 用于LRU
  private config: CacheConfig;
  private accessCounter = 0;

  constructor(config?: Partial<CacheConfig>) {
    this.config = {
      defaultTTL: 5 * 60 * 1000, // 5分钟
      maxSize: 100,
      enablePersistence: true,
      storagePrefix: 'cache_',
      ...config,
    };
  }

  /**
   * 设置缓存
   */
  set<T>(key: string, data: T, ttl?: number): void {
    const actualTTL = ttl ?? this.config.defaultTTL;
    const entry: CacheEntry<T> = {
      data,
      timestamp: Date.now(),
      ttl: actualTTL,
    };

    // 内存缓存
    this.memoryCache.set(key, entry);
    this.accessOrder.set(key, ++this.accessCounter);

    // 检查缓存大小限制
    this.enforceMaxSize();

    // 持久化缓存
    if (this.config.enablePersistence) {
      this.persistToStorage(key, entry);
    }

    logger.debug('Cache set:', { key, ttl: actualTTL });
  }

  /**
   * 获取缓存
   */
  get<T>(key: string): T | null {
    // 先从内存缓存获取
    let entry = this.memoryCache.get(key);

    // 如果内存中没有，尝试从持久化存储获取
    if (!entry && this.config.enablePersistence) {
      const storedEntry = this.getFromStorage(key);
      if (storedEntry) {
        entry = storedEntry;
        this.memoryCache.set(key, entry);
      }
    }

    if (!entry) {
      logger.debug('Cache miss:', { key });
      return null;
    }

    // 检查是否过期
    if (this.isExpired(entry)) {
      this.delete(key);
      logger.debug('Cache expired:', { key });
      return null;
    }

    // 更新访问顺序
    this.accessOrder.set(key, ++this.accessCounter);

    logger.debug('Cache hit:', { key });
    return entry.data as T;
  }

  /**
   * 删除缓存
   */
  delete(key: string): boolean {
    const hasMemory = this.memoryCache.delete(key);
    this.accessOrder.delete(key);

    if (this.config.enablePersistence) {
      this.removeFromStorage(key);
    }

    if (hasMemory) {
      logger.debug('Cache deleted:', { key });
    }

    return hasMemory;
  }

  /**
   * 检查缓存是否存在且未过期
   */
  has(key: string): boolean {
    return this.get(key) !== null;
  }

  /**
   * 清空所有缓存
   */
  clear(): void {
    this.memoryCache.clear();
    this.accessOrder.clear();
    this.accessCounter = 0;

    if (this.config.enablePersistence) {
      this.clearStorage();
    }

    logger.debug('Cache cleared');
  }

  /**
   * 获取缓存统计信息
   */
  getStats() {
    const memorySize = this.memoryCache.size;
    const expiredCount = Array.from(this.memoryCache.values()).filter(entry =>
      this.isExpired(entry)
    ).length;

    return {
      memorySize,
      expiredCount,
      maxSize: this.config.maxSize,
      accessCounter: this.accessCounter,
    };
  }

  /**
   * 清理过期缓存
   */
  cleanup(): void {
    const expiredKeys: string[] = [];

    for (const [key, entry] of this.memoryCache.entries()) {
      if (this.isExpired(entry)) {
        expiredKeys.push(key);
      }
    }

    expiredKeys.forEach(key => this.delete(key));

    if (expiredKeys.length > 0) {
      logger.debug('Cache cleanup:', { expiredCount: expiredKeys.length });
    }
  }

  /**
   * 检查缓存条目是否过期
   */
  private isExpired(entry: CacheEntry): boolean {
    return Date.now() - entry.timestamp > entry.ttl;
  }

  /**
   * 强制执行最大缓存大小限制（LRU淘汰）
   */
  private enforceMaxSize(): void {
    if (this.memoryCache.size <= this.config.maxSize) {
      return;
    }

    // 找到最少使用的缓存条目
    const sortedByAccess = Array.from(this.accessOrder.entries())
      .sort((a, b) => a[1] - b[1]);

    const toRemove = sortedByAccess.slice(0, this.memoryCache.size - this.config.maxSize);

    toRemove.forEach(([key]) => {
      this.delete(key);
    });

    logger.debug('Cache LRU eviction:', { removedCount: toRemove.length });
  }

  /**
   * 持久化到本地存储
   */
  private persistToStorage(key: string, entry: CacheEntry): void {
    if (typeof window === 'undefined') return;

    try {
      const storageKey = this.config.storagePrefix + key;
      localStorage.setItem(storageKey, JSON.stringify(entry));
    } catch (error) {
      logger.warn('Failed to persist cache to storage:', { key, error });
    }
  }

  /**
   * 从本地存储获取
   */
  private getFromStorage(key: string): CacheEntry | null {
    if (typeof window === 'undefined') return null;

    try {
      const storageKey = this.config.storagePrefix + key;
      const stored = localStorage.getItem(storageKey);
      return stored ? JSON.parse(stored) : null;
    } catch (error) {
      logger.warn('Failed to get cache from storage:', { key, error });
      return null;
    }
  }

  /**
   * 从本地存储删除
   */
  private removeFromStorage(key: string): void {
    if (typeof window === 'undefined') return;

    try {
      const storageKey = this.config.storagePrefix + key;
      localStorage.removeItem(storageKey);
    } catch (error) {
      logger.warn('Failed to remove cache from storage:', { key, error });
    }
  }

  /**
   * 清空本地存储中的缓存
   */
  private clearStorage(): void {
    if (typeof window === 'undefined') return;

    try {
      const keys = Object.keys(localStorage);
      const cacheKeys = keys.filter(key => key.startsWith(this.config.storagePrefix));
      cacheKeys.forEach(key => localStorage.removeItem(key));
    } catch (error) {
      logger.warn('Failed to clear cache storage:', error);
    }
  }
}

// 创建默认缓存管理器实例
export const cacheManager = new CacheManager();

// 设置定期清理过期缓存
if (typeof window !== 'undefined') {
  setInterval(() => {
    cacheManager.cleanup();
  }, 60000); // 每分钟清理一次
}

// 导出类型
export { CacheManager };
