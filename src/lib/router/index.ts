import { useRouter as useNextRouter, usePathname } from 'next/navigation';
import { useAppStore } from '@/stores';
import { ROUTE_CONSTANTS } from '@/constants';
import { logger } from '@/lib/logger';

/**
 * 路由守卫配置
 */
export interface RouteGuard {
  path: string;
  auth?: boolean;
  roles?: string[];
  permissions?: string[];
  redirect?: string;
}

/**
 * 扩展的路由hook
 * 提供路由守卫、权限检查等功能
 */
export function useRouter() {
  const router = useNextRouter();
  const pathname = usePathname();
  const { isAuthenticated, user } = useAppStore();

  /**
   * 检查路由权限
   */
  const checkRoutePermission = (guard: RouteGuard): boolean => {
    // 检查是否需要认证
    if (guard.auth && !isAuthenticated) {
      logger.warn('Route access denied: not authenticated', { path: guard.path });
      return false;
    }

    // 检查角色权限
    if (guard.roles && guard.roles.length > 0) {
      if (!user || !guard.roles.some(role => user.roles.includes(role))) {
        logger.warn('Route access denied: insufficient roles', {
          path: guard.path,
          requiredRoles: guard.roles,
          userRoles: user?.roles
        });
        return false;
      }
    }

    // 检查具体权限
    if (guard.permissions && guard.permissions.length > 0) {
      if (!user || !guard.permissions.some(permission => user.roles.includes(permission))) {
        logger.warn('Route access denied: insufficient permissions', {
          path: guard.path,
          requiredPermissions: guard.permissions,
          userPermissions: user?.roles
        });
        return false;
      }
    }

    return true;
  };

  /**
   * 安全导航 - 带权限检查的路由跳转
   */
  const safePush = (path: string, guard?: RouteGuard) => {
    const routeGuard = guard || getRouteGuard(path);

    if (routeGuard && !checkRoutePermission(routeGuard)) {
      const redirectPath = routeGuard.redirect || '/login';
      logger.info('Redirecting due to permission check', {
        originalPath: path,
        redirectPath
      });
      router.push(redirectPath);
      return;
    }

    logger.info('Navigating to route', { path });
    router.push(path);
  };

  /**
   * 安全替换 - 带权限检查的路由替换
   */
  const safeReplace = (path: string, guard?: RouteGuard) => {
    const routeGuard = guard || getRouteGuard(path);

    if (routeGuard && !checkRoutePermission(routeGuard)) {
      const redirectPath = routeGuard.redirect || '/login';
      logger.info('Redirecting due to permission check', {
        originalPath: path,
        redirectPath
      });
      router.replace(redirectPath);
      return;
    }

    logger.info('Replacing route', { path });
    router.replace(path);
  };

  /**
   * 返回上一页
   */
  const goBack = () => {
    logger.info('Navigating back');
    router.back();
  };

  /**
   * 前进到下一页
   */
  const goForward = () => {
    logger.info('Navigating forward');
    router.forward();
  };

  /**
   * 刷新当前页面
   */
  const refreshPage = () => {
    logger.info('Refreshing current page');
    router.refresh();
  };

  return {
    // Next.js原生方法
    push: router.push,
    replace: router.replace,
    back: router.back,
    forward: router.forward,
    refresh: router.refresh,
    prefetch: router.prefetch,

    // 扩展方法
    safePush,
    safeReplace,
    goBack,
    goForward,
    refreshPage,
    checkRoutePermission,

    // 当前路径
    pathname,
  };
}

/**
 * 获取路由守卫配置
 */
function getRouteGuard(path: string): RouteGuard | null {
  // 检查是否为公共路由
  if (ROUTE_CONSTANTS.PUBLIC_ROUTES.includes(path as any)) {
    return null;
  }

  // 检查是否为认证路由
  if (ROUTE_CONSTANTS.AUTH_ROUTES.some(route => path.startsWith(route))) {
    return {
      path,
      auth: true,
      redirect: '/login',
    };
  }

  // 检查是否为管理员路由
  if (ROUTE_CONSTANTS.ADMIN_ROUTES.some(route => path.startsWith(route))) {
    return {
      path,
      auth: true,
      roles: ['admin'],
      redirect: '/login',
    };
  }

  // 默认需要认证
  return {
    path,
    auth: true,
    redirect: '/login',
  };
}

/**
 * 路由守卫hook
 * 用于在组件中进行路由权限检查
 */
export function useRouteGuard(guard?: RouteGuard) {
  const pathname = usePathname();
  const { isAuthenticated, user } = useAppStore();
  const router = useNextRouter();

  const routeGuard = guard || getRouteGuard(pathname);

  // 检查当前路由权限
  const hasPermission = routeGuard ? checkRoutePermission(routeGuard, isAuthenticated, user) : true;

  // 如果没有权限，自动重定向
  if (routeGuard && !hasPermission) {
    const redirectPath = routeGuard.redirect || '/login';
    router.replace(redirectPath);
  }

  return {
    hasPermission,
    isLoading: false, // 可以根据需要添加加载状态
  };
}

/**
 * 检查路由权限的纯函数
 */
function checkRoutePermission(
  guard: RouteGuard,
  isAuthenticated: boolean,
  user: any
): boolean {
  // 检查是否需要认证
  if (guard.auth && !isAuthenticated) {
    return false;
  }

  // 检查角色权限
  if (guard.roles && guard.roles.length > 0) {
    if (!user || !guard.roles.some(role => user.roles.includes(role))) {
      return false;
    }
  }

  // 检查具体权限
  if (guard.permissions && guard.permissions.length > 0) {
    if (!user || !guard.permissions.some(permission => user.roles.includes(permission))) {
      return false;
    }
  }

  return true;
}

/**
 * 获取面包屑导航
 */
export function useBreadcrumb() {
  const pathname = usePathname();

  const getBreadcrumb = () => {
    const segments = pathname.split('/').filter(Boolean);
    const breadcrumb = segments.map((segment, index) => {
      const path = '/' + segments.slice(0, index + 1).join('/');
      return {
        title: formatSegmentTitle(segment),
        path,
        active: index === segments.length - 1,
      };
    });

    // 添加首页
    return [
      { title: '首页', path: '/', active: pathname === '/' },
      ...breadcrumb,
    ];
  };

  return {
    breadcrumb: getBreadcrumb(),
  };
}

/**
 * 格式化路径段标题
 */
function formatSegmentTitle(segment: string): string {
  // 这里可以根据实际需要进行映射
  const titleMap: Record<string, string> = {
    dashboard: '仪表板',
    profile: '个人资料',
    settings: '设置',
    admin: '管理',
    users: '用户管理',
    // 可以继续添加更多映射
  };

  return titleMap[segment] || segment;
}
