import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { logger } from '@/lib/logger';

/**
 * API响应数据结构
 */
export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
  success: boolean;
}

/**
 * API请求配置
 */
export interface ApiRequestConfig extends AxiosRequestConfig {
  skipAuth?: boolean;
  skipLoading?: boolean;
  skipErrorHandler?: boolean;
}

/**
 * API客户端类
 * 提供统一的HTTP请求封装，包含请求/响应拦截器、错误处理、认证等功能
 */
class ApiClient {
  private instance: AxiosInstance;
  private loadingCount = 0;

  constructor(baseURL?: string) {
    this.instance = axios.create({
      baseURL: baseURL || process.env.NEXT_PUBLIC_API_BASE_URL || '/api',
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    this.setupInterceptors();
  }

  /**
   * 设置请求和响应拦截器
   */
  private setupInterceptors() {
    // 请求拦截器
    this.instance.interceptors.request.use(
      (config: any) => {
        const customConfig = config as ApiRequestConfig;
        
        // 添加认证token
        if (!customConfig.skipAuth) {
          const token = this.getAuthToken();
          if (token) {
            config.headers.Authorization = `Bearer ${token}`;
          }
        }

        // 显示加载状态
        if (!customConfig.skipLoading) {
          this.showLoading();
        }

        // 记录请求日志
        logger.info('API Request:', {
          method: config.method?.toUpperCase(),
          url: config.url,
          params: config.params,
          data: config.data,
        });

        return config;
      },
      (error) => {
        logger.error('API Request Error:', error);
        return Promise.reject(error);
      }
    );

    // 响应拦截器
    this.instance.interceptors.response.use(
      (response: AxiosResponse) => {
        const customConfig = response.config as ApiRequestConfig;
        
        // 隐藏加载状态
        if (!customConfig.skipLoading) {
          this.hideLoading();
        }

        // 记录响应日志
        logger.info('API Response:', {
          status: response.status,
          url: response.config.url,
          data: response.data,
        });

        // 统一处理业务错误
        const apiResponse = response.data as ApiResponse;
        if (apiResponse && typeof apiResponse.success === 'boolean') {
          if (!apiResponse.success) {
            const error = new Error(apiResponse.message || '请求失败');
            (error as any).code = apiResponse.code;
            throw error;
          }
        }

        return response;
      },
      (error) => {
        const customConfig = error.config as ApiRequestConfig;
        
        // 隐藏加载状态
        if (!customConfig?.skipLoading) {
          this.hideLoading();
        }

        // 记录错误日志
        logger.error('API Response Error:', {
          status: error.response?.status,
          url: error.config?.url,
          message: error.message,
          data: error.response?.data,
        });

        // 统一错误处理
        if (!customConfig?.skipErrorHandler) {
          this.handleError(error);
        }

        return Promise.reject(error);
      }
    );
  }

  /**
   * 获取认证token
   */
  private getAuthToken(): string | null {
    if (typeof window !== 'undefined') {
      return localStorage.getItem('auth_token');
    }
    return null;
  }

  /**
   * 显示加载状态
   */
  private showLoading() {
    this.loadingCount++;
    // 这里可以集成全局loading组件
    if (typeof window !== 'undefined') {
      document.body.style.cursor = 'wait';
    }
  }

  /**
   * 隐藏加载状态
   */
  private hideLoading() {
    this.loadingCount = Math.max(0, this.loadingCount - 1);
    if (this.loadingCount === 0 && typeof window !== 'undefined') {
      document.body.style.cursor = 'default';
    }
  }

  /**
   * 统一错误处理
   */
  private handleError(error: any) {
    let message = '网络请求失败';
    
    if (error.response) {
      const status = error.response.status;
      switch (status) {
        case 401:
          message = '未授权，请重新登录';
          // 可以在这里触发登录页面跳转
          break;
        case 403:
          message = '拒绝访问';
          break;
        case 404:
          message = '请求的资源不存在';
          break;
        case 500:
          message = '服务器内部错误';
          break;
        default:
          message = error.response.data?.message || `请求失败 (${status})`;
      }
    } else if (error.request) {
      message = '网络连接失败，请检查网络';
    } else {
      message = error.message || '请求失败';
    }

    // 这里可以集成全局提示组件
    console.error('API Error:', message);
  }

  /**
   * GET请求
   */
  async get<T = any>(url: string, config?: ApiRequestConfig): Promise<T> {
    const response = await this.instance.get<ApiResponse<T>>(url, config);
    return response.data.data;
  }

  /**
   * POST请求
   */
  async post<T = any>(url: string, data?: any, config?: ApiRequestConfig): Promise<T> {
    const response = await this.instance.post<ApiResponse<T>>(url, data, config);
    return response.data.data;
  }

  /**
   * PUT请求
   */
  async put<T = any>(url: string, data?: any, config?: ApiRequestConfig): Promise<T> {
    const response = await this.instance.put<ApiResponse<T>>(url, data, config);
    return response.data.data;
  }

  /**
   * DELETE请求
   */
  async delete<T = any>(url: string, config?: ApiRequestConfig): Promise<T> {
    const response = await this.instance.delete<ApiResponse<T>>(url, config);
    return response.data.data;
  }

  /**
   * PATCH请求
   */
  async patch<T = any>(url: string, data?: any, config?: ApiRequestConfig): Promise<T> {
    const response = await this.instance.patch<ApiResponse<T>>(url, data, config);
    return response.data.data;
  }

  /**
   * 获取原始axios实例
   */
  getInstance(): AxiosInstance {
    return this.instance;
  }
}

// 创建默认API客户端实例
export const apiClient = new ApiClient();

// 导出便捷方法
export const { get, post, put, delete: del, patch } = apiClient;
