/**
 * @cvte/mobile-framework
 * 移动端底座框架 - 主入口文件
 *
 * 基于Next.js 15构建的移动端底座项目，提供完整的基础设施和组件库
 * 支持插件化配置，业务端可快速初始化和定制
 */

// ==================== 核心库导出 ====================
export * from './lib';

// ==================== 组件库导出 ====================
export * from './components';

// ==================== Hooks导出 ====================
export * from './hooks';

// ==================== 工具函数导出 ====================
export * from './utils';

// ==================== 类型定义导出 ====================
// 注意：避免与lib模块中的类型冲突，types模块通过单独导入使用
// export * from './types';

// ==================== 常量导出 ====================
export * from './constants';

// ==================== 状态管理导出 ====================
export * from './stores';

// ==================== 样式导出 ====================
import './styles/index.css';

// ==================== 框架配置和初始化 ====================
export { default as MobileFrameworkProvider } from './providers/MobileFrameworkProvider';
export { default as createMobileApp } from './core/createMobileApp';
export { default as initializeFramework } from './core/initializeFramework';

// ==================== 插件系统导出 ====================
export * from './plugins';

// ==================== 版本信息 ====================
export const VERSION = '0.1.0';

// ==================== 默认导出 ====================
export { default } from './core/createMobileApp';
