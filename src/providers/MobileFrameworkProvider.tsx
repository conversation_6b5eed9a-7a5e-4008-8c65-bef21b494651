'use client';

import React, { createContext, useContext, useEffect, ReactNode } from 'react';
import { ConfigProvider } from 'antd-mobile';
import zhCN from 'antd-mobile/es/locales/zh-CN';
import { MobileAppConfig, MobileAppInstance } from '@/core/createMobileApp';
import initializeFramework from '@/core/initializeFramework';
import { logger } from '@/lib/logger';

/**
 * 框架上下文接口
 */
interface MobileFrameworkContextType {
  app: MobileAppInstance | null;
  config: MobileAppConfig | null;
  isInitialized: boolean;
}

/**
 * 框架上下文
 */
const MobileFrameworkContext = createContext<MobileFrameworkContextType>({
  app: null,
  config: null,
  isInitialized: false,
});

/**
 * 框架提供者组件属性
 */
interface MobileFrameworkProviderProps {
  app: MobileAppInstance;
  children: ReactNode;
}

/**
 * 移动框架提供者组件
 * 为整个应用提供框架上下文和配置
 */
const MobileFrameworkProvider: React.FC<MobileFrameworkProviderProps> = ({
  app,
  children,
}) => {
  const [isInitialized, setIsInitialized] = React.useState(false);

  useEffect(() => {
    const initialize = async () => {
      try {
        await initializeFramework(app.config);
        setIsInitialized(true);
        logger.info('MobileFrameworkProvider initialized');
      } catch (error) {
        logger.error('Failed to initialize MobileFrameworkProvider', error);
      }
    };

    initialize();

    // 清理函数
    return () => {
      app.destroy();
    };
  }, [app]);

  const contextValue: MobileFrameworkContextType = {
    app,
    config: app.config,
    isInitialized,
  };

  return (
    <MobileFrameworkContext.Provider value={contextValue}>
      <ConfigProvider locale={zhCN}>
        <div className="mobile-framework">
          {children}
        </div>
      </ConfigProvider>
    </MobileFrameworkContext.Provider>
  );
};

/**
 * 使用框架上下文的Hook
 */
export const useMobileFramework = (): MobileFrameworkContextType => {
  const context = useContext(MobileFrameworkContext);
  
  if (!context) {
    throw new Error('useMobileFramework must be used within a MobileFrameworkProvider');
  }
  
  return context;
};

export default MobileFrameworkProvider;
