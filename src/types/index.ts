/**
 * 基础类型定义
 */

/**
 * 通用响应结构
 */
export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
  success: boolean;
  timestamp?: number;
}

/**
 * 分页请求参数
 */
export interface PaginationParams {
  page: number;
  pageSize: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

/**
 * 分页响应数据
 */
export interface PaginationResponse<T = any> {
  list: T[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

/**
 * 用户信息
 */
export interface User {
  id: string;
  username: string;
  email: string;
  phone?: string;
  avatar?: string;
  nickname?: string;
  realName?: string;
  roles: string[];
  permissions: string[];
  status: 'active' | 'inactive' | 'banned';
  createdAt: string;
  updatedAt: string;
  lastLoginAt?: string;
}

/**
 * 登录请求参数
 */
export interface LoginParams {
  username: string;
  password: string;
  captcha?: string;
  remember?: boolean;
}

/**
 * 登录响应数据
 */
export interface LoginResponse {
  user: User;
  token: string;
  refreshToken?: string;
  expiresIn: number;
}

/**
 * 菜单项
 */
export interface MenuItem {
  id: string;
  title: string;
  icon?: string;
  path?: string;
  children?: MenuItem[];
  permissions?: string[];
  hidden?: boolean;
  disabled?: boolean;
  badge?: string | number;
}

/**
 * 表单字段配置
 */
export interface FormField {
  name: string;
  label: string;
  type: 'text' | 'password' | 'email' | 'number' | 'textarea' | 'select' | 'radio' | 'checkbox' | 'date' | 'file';
  required?: boolean;
  placeholder?: string;
  defaultValue?: any;
  options?: Array<{ label: string; value: any }>;
  validation?: {
    min?: number;
    max?: number;
    pattern?: RegExp;
    message?: string;
  };
  disabled?: boolean;
  hidden?: boolean;
  span?: number; // 栅格占位
}

/**
 * 表单配置
 */
export interface FormConfig {
  fields: FormField[];
  layout?: 'horizontal' | 'vertical' | 'inline';
  labelCol?: number;
  wrapperCol?: number;
  submitText?: string;
  resetText?: string;
  showReset?: boolean;
}

/**
 * 表格列配置
 */
export interface TableColumn<T = any> {
  key: string;
  title: string;
  dataIndex?: string;
  width?: number | string;
  align?: 'left' | 'center' | 'right';
  fixed?: 'left' | 'right';
  sortable?: boolean;
  filterable?: boolean;
  render?: (value: any, record: T, index: number) => React.ReactNode;
  hidden?: boolean;
}

/**
 * 表格配置
 */
export interface TableConfig<T = any> {
  columns: TableColumn<T>[];
  rowKey?: string;
  pagination?: boolean | PaginationParams;
  selection?: {
    type: 'checkbox' | 'radio';
    onChange?: (selectedRowKeys: string[], selectedRows: T[]) => void;
  };
  actions?: Array<{
    key: string;
    title: string;
    icon?: string;
    onClick: (record: T) => void;
    disabled?: (record: T) => boolean;
    hidden?: (record: T) => boolean;
  }>;
}

/**
 * 文件信息
 */
export interface FileInfo {
  id: string;
  name: string;
  size: number;
  type: string;
  url: string;
  thumbnailUrl?: string;
  uploadedAt: string;
  uploadedBy: string;
}

/**
 * 上传配置
 */
export interface UploadConfig {
  accept?: string[];
  maxSize?: number;
  maxCount?: number;
  multiple?: boolean;
  directory?: boolean;
  beforeUpload?: (file: File) => boolean | Promise<boolean>;
  onProgress?: (percent: number, file: File) => void;
  onSuccess?: (response: any, file: File) => void;
  onError?: (error: Error, file: File) => void;
}

/**
 * 通知消息
 */
export interface Notification {
  id: string;
  type: 'info' | 'success' | 'warning' | 'error';
  title: string;
  message?: string;
  duration?: number;
  closable?: boolean;
  actions?: Array<{
    text: string;
    onClick: () => void;
  }>;
  createdAt: string;
  read?: boolean;
}

/**
 * 主题配置
 */
export interface ThemeConfig {
  mode: 'light' | 'dark';
  primaryColor: string;
  borderRadius: number;
  fontSize: number;
  fontFamily: string;
}

/**
 * 应用配置
 */
export interface AppConfig {
  name: string;
  version: string;
  logo?: string;
  favicon?: string;
  theme: ThemeConfig;
  features: {
    enableDarkMode: boolean;
    enableI18n: boolean;
    enablePWA: boolean;
    enableOffline: boolean;
  };
  api: {
    baseURL: string;
    timeout: number;
    retryCount: number;
  };
  upload: {
    maxSize: number;
    allowedTypes: string[];
    endpoint: string;
  };
}

/**
 * 路由信息
 */
export interface RouteInfo {
  path: string;
  title: string;
  component?: React.ComponentType;
  exact?: boolean;
  auth?: boolean;
  roles?: string[];
  permissions?: string[];
  meta?: Record<string, any>;
}

/**
 * 错误信息
 */
export interface ErrorInfo {
  code: string;
  message: string;
  details?: any;
  stack?: string;
  timestamp: string;
  url?: string;
  userAgent?: string;
}

/**
 * 日志条目
 */
export interface LogEntry {
  id: string;
  level: 'debug' | 'info' | 'warn' | 'error';
  message: string;
  data?: any;
  timestamp: string;
  source?: string;
  userId?: string;
}

/**
 * 统计数据
 */
export interface Statistics {
  key: string;
  label: string;
  value: number;
  unit?: string;
  trend?: 'up' | 'down' | 'stable';
  change?: number;
  changePercent?: number;
}

/**
 * 图表数据
 */
export interface ChartData {
  type: 'line' | 'bar' | 'pie' | 'area' | 'scatter';
  title?: string;
  data: Array<{
    name: string;
    value: number;
    [key: string]: any;
  }>;
  xAxis?: string;
  yAxis?: string;
  colors?: string[];
  options?: Record<string, any>;
}

/**
 * 搜索参数
 */
export interface SearchParams {
  keyword?: string;
  filters?: Record<string, any>;
  dateRange?: [string, string];
  status?: string;
  category?: string;
  tags?: string[];
}

/**
 * 操作记录
 */
export interface OperationLog {
  id: string;
  userId: string;
  userName: string;
  action: string;
  resource: string;
  resourceId?: string;
  details?: Record<string, any>;
  ip: string;
  userAgent: string;
  timestamp: string;
  status: 'success' | 'failed';
  error?: string;
}
