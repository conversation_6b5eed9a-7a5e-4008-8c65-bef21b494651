import resolve from '@rollup/plugin-node-resolve';
import commonjs from '@rollup/plugin-commonjs';
import typescript from '@rollup/plugin-typescript';
import json from '@rollup/plugin-json';
import peerDepsExternal from 'rollup-plugin-peer-deps-external';
import postcss from 'rollup-plugin-postcss';
import { dts } from 'rollup-plugin-dts';
import { readFileSync } from 'fs';

const packageJson = JSON.parse(readFileSync('./package.json', 'utf8'));

/**
 * 基础插件配置
 */
const basePlugins = [
  peerDepsExternal(), // 排除peer dependencies
  resolve({
    browser: true,
    preferBuiltins: false,
  }),
  commonjs(),
  json(),
  postcss({
    extract: true,
    minimize: true,
    sourceMap: true,
  }),
];

/**
 * TypeScript插件配置
 */
const typescriptPlugin = typescript({
  tsconfig: './tsconfig.build.json',
  declaration: false, // 类型声明文件单独生成
  declarationMap: false,
  outputToFilesystem: false,
});

/**
 * 外部依赖配置
 */
const external = [
  ...Object.keys(packageJson.peerDependencies || {}),
  ...Object.keys(packageJson.dependencies || {}),
  'react/jsx-runtime',
  'next/router',
  'next/navigation',
  'next/image',
  'next/link',
];

/**
 * 主入口构建配置
 */
const mainConfig = {
  input: 'src/index.ts',
  output: [
    {
      file: packageJson.main,
      format: 'cjs',
      sourcemap: true,
      exports: 'named',
    },
    {
      file: packageJson.module,
      format: 'esm',
      sourcemap: true,
      exports: 'named',
    },
  ],
  plugins: [...basePlugins, typescriptPlugin],
  external,
};

/**
 * 子模块构建配置
 */
const subModuleConfigs = [
  {
    name: 'components',
    input: 'src/components/index.ts',
  },
  {
    name: 'lib',
    input: 'src/lib/index.ts',
  },
  {
    name: 'hooks',
    input: 'src/hooks/index.ts',
  },
  {
    name: 'utils',
    input: 'src/utils/index.ts',
  },
].map(({ name, input }) => ({
  input,
  output: [
    {
      file: `dist/${name}/index.js`,
      format: 'cjs',
      sourcemap: true,
      exports: 'named',
    },
    {
      file: `dist/${name}/index.esm.js`,
      format: 'esm',
      sourcemap: true,
      exports: 'named',
    },
  ],
  plugins: [...basePlugins, typescriptPlugin],
  external,
}));

/**
 * 类型声明文件构建配置
 */
const dtsConfig = {
  input: 'src/index.ts',
  output: {
    file: packageJson.types,
    format: 'esm',
  },
  plugins: [dts()],
  external: [/\.css$/],
};

/**
 * 子模块类型声明文件构建配置
 */
const subModuleDtsConfigs = [
  {
    name: 'components',
    input: 'src/components/index.ts',
  },
  {
    name: 'lib',
    input: 'src/lib/index.ts',
  },
  {
    name: 'hooks',
    input: 'src/hooks/index.ts',
  },
  {
    name: 'utils',
    input: 'src/utils/index.ts',
  },
].map(({ name, input }) => ({
  input,
  output: {
    file: `dist/${name}/index.d.ts`,
    format: 'esm',
  },
  plugins: [dts()],
  external: [/\.css$/],
}));

export default [
  mainConfig,
  ...subModuleConfigs,
  dtsConfig,
  ...subModuleDtsConfigs,
];
